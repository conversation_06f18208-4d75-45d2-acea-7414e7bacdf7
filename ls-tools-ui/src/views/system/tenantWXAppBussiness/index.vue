<template>
    <div class="app-container">
        <el-form :model="queryParams" ref="queryForm" :inline="true" v-show="showSearch" label-width="68px">
            <el-form-item label="租户名称" prop="tenantId" v-if="isTenant">
                <el-select v-model="queryParams.tenantId" placeholder="请选择租户" clearable>
                    <el-option v-for="item in tenantOptions" :key="item.value" :label="item.label"
                        :value="item.value" />
                </el-select>
            </el-form-item>
            <el-form-item>
                <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
                <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
            </el-form-item>
        </el-form>

        <el-row :gutter="10" class="mb8">
            <el-col :span="1.5">
                <el-button type="primary" plain icon="el-icon-plus" size="mini" @click="handleAdd"
                    v-hasPermi="['tenantWXAppMoudles:tenantWXAppBussiness:add']">新增</el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button type="success" plain icon="el-icon-edit" size="mini" :disabled="single" @click="handleUpdate"
                    v-hasPermi="['tenantWXAppMoudles:tenantWXAppBussiness:edit']">修改</el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button type="danger" plain icon="el-icon-delete" size="mini" :disabled="multiple"
                    @click="handleDelete" v-hasPermi="['tenantWXAppMoudles:tenantWXAppBussiness:del']">删除</el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button type="warning" plain icon="el-icon-download" size="mini" :loading="exportLoading"
                    @click="handleExport" v-hasPermi="['tenantWXAppMoudles:tenantWXAppBussiness:export']">导出</el-button>
            </el-col>
            <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
        </el-row>

        <el-table v-loading="loading" :data="tenantWXAppBussinessList" @selection-change="handleSelectionChange">
            <el-table-column type="selection" width="55" align="center" />
            <el-table-column label="" align="center" prop="id" />
            <el-table-column label="租户名称" align="center" prop="tenantName" v-if="isTenant" />
            <el-table-column label="小程序id" align="center" prop="appId" />
            <el-table-column label="小程序密钥" align="center" prop="appSecret" />
            <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
                <template slot-scope="scope">
                    <el-button size="mini" type="text" icon="el-icon-edit" @click="handleUpdate(scope.row)"
                        v-hasPermi="['tenantWXAppMoudles:tenantWXAppBussiness:edit']">修改</el-button>
                    <el-button size="mini" type="text" icon="el-icon-delete" @click="handleDelete(scope.row)"
                        v-hasPermi="['tenantWXAppMoudles:tenantWXAppBussiness:del']">删除</el-button>
                </template>
            </el-table-column>
        </el-table>

        <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum"
            :limit.sync="queryParams.pageSize" />

        <!-- 添加或修改tenantWXAppMethod对话框 -->
        <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
            <el-form ref="form" :model="form" :rules="rules" label-width="80px">
                <el-form-item label="租户名称" prop="tenantId" v-if="isTenant">
                    <el-select v-model="form.tenantId" placeholder="请选择租户" clearable>
                        <el-option v-for="item in tenantOptions" :key="item.value" :label="item.label"
                            :value="item.value" />
                    </el-select>
                </el-form-item>
                <el-form-item label="小程序id" prop="appId">
                    <el-input v-model="form.appId" placeholder="请输入小程序id" />
                </el-form-item>
                <el-form-item label="小程序密钥" prop="appSecret">
                    <el-input v-model="form.appSecret" placeholder="请输入小程序密钥" />
                </el-form-item>
            </el-form>
            <div slot="footer" class="dialog-footer">
                <el-button type="primary" @click="submitForm">确 定</el-button>
                <el-button @click="cancel">取 消</el-button>
            </div>
        </el-dialog>
    </div>
</template>

<script>
import { listTenantWXAppBussiness, getTenantWXAppBussiness, delTenantWXAppBussiness, addTenantWXAppBussiness, updateTenantWXAppBussiness, exportTenantWXAppBussiness } from "@/api/system/tenantWXAppBussiness";
import { exportExcel } from "@/utils/zipdownload";
import { getTenantId } from '@/utils/auth'
import { getTenantOptions } from '@/api/system/tenant'
export default {
    name: "TenantWXAppBussiness",
    computed: {
        isTenant() {
            return getTenantId() == undefined || getTenantId() == null || getTenantId() == ''
        }
    },
    data() {
        return {
            tenantOptions: [] ,// 租户下拉选项
            // 遮罩层
            loading: true,
            // 导出遮罩层
            exportLoading: false,
            // 选中数组
            ids: [],
            // 非单个禁用
            single: true,
            // 非多个禁用
            multiple: true,
            // 显示搜索条件
            showSearch: true,
            // 总条数
            total: 0,
            // tenantWXAppMethod表格数据
            tenantWXAppBussinessList: [],
            // 弹出层标题
            title: "",
            // 是否显示弹出层
            open: false,
            // 查询参数
            queryParams: {
                pageNum: 1,
                pageSize: 10,
            },
            // 表单参数
            form: {},
            // 表单校验
            rules: {
                tenantId: [
                    { required: true, message: "租户id不能为空", trigger: "change" }
                ],
                appId: [
                    { required: true, message: "小程序id不能为空", trigger: "blur" }
                ],
                appSecret: [
                    { required: true, message: "小程序密钥不能为空", trigger: "blur" }
                ],
            }
        };
    },
    created() {
        this.getList();
        if (this.isTenant) {
            this.getTenantOptions()
        }
    },
    methods: {
        getTenantOptions() {
        getTenantOptions().then(response => {
          this.tenantOptions = response.data
        })
      },
        /** 查询tenantWXAppMethod列表 */
        getList() {
            this.loading = true;
            listTenantWXAppBussiness(this.queryParams).then(response => {
                this.tenantWXAppBussinessList = response.rows;
                this.total = response.total;
                this.loading = false;
            });
        },
        // 取消按钮
        cancel() {
            this.open = false;
            this.reset();
        },
        // 表单重置
        reset() {
            this.form = {
                id: null,
                tenantId: null,
                appId: null,
                appSecret: null,
                isDeleted: null
            };
            this.resetForm("form");
        },
        /** 搜索按钮操作 */
        handleQuery() {
            this.queryParams.pageNum = 1;
            this.getList();
        },
        /** 重置按钮操作 */
        resetQuery() {
            this.resetForm("queryForm");
            this.handleQuery();
        },
        // 多选框选中数据
        handleSelectionChange(selection) {
            this.ids = selection.map(item => item.id)
            this.single = selection.length !== 1
            this.multiple = !selection.length
        },
        /** 新增按钮操作 */
        handleAdd() {
            this.reset();
            this.open = true;
            this.title = "添加";
        },
        /** 修改按钮操作 */
        handleUpdate(row) {
            this.reset();
            const id = row.id || this.ids
            getTenantWXAppBussiness(id).then(response => {
                this.form = response.data;
                this.open = true;
                this.title = "修改";
            });
        },
        /** 提交按钮 */
        submitForm() {
            this.$refs["form"].validate(valid => {
                if (valid) {
                    if (this.form.id != null) {
                        updateTenantWXAppBussiness(this.form).then(response => {
                            this.msgSuccess("修改成功");
                            this.open = false;
                            this.getList();
                        });
                    } else {
                        addTenantWXAppBussiness(this.form).then(response => {
                            this.msgSuccess("新增成功");
                            this.open = false;
                            this.getList();
                        });
                    }
                }
            });
        },
        /** 删除按钮操作 */
        handleDelete(row) {
            const ids = row.id || this.ids;
            this.$confirm('是否确认删除tenantWXAppMethod编号为"' + ids + '"的数据项?', "警告", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning"
            }).then(function () {
                return delTenantWXAppBussiness(ids);
            }).then(() => {
                this.getList();
                this.msgSuccess("删除成功");
            }).catch(() => { });
        },
        /** 导出按钮操作 */
        handleExport() {
            const queryParams = this.queryParams;
            this.$confirm('是否确认导出所有tenantWXAppMethod数据项?', "警告", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning"
            }).then(() => {
                this.exportLoading = true;
                // 开始导出
                exportExcel("/tenantWXAppMoudles/tenant_wx_app/export", this.queryParams);
                this.exportLoading = false;
            }).catch(() => { });
        }
    }
};
</script>
