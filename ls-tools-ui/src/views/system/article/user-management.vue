<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="租户" prop="tenantId" v-if="isAdmin">
        <el-select v-model="queryParams.tenantId" placeholder="请选择租户" clearable>
          <el-option
            v-for="item in tenantOptions"
            :key="item.tenantId"
            :label="item.name"
            :value="item.tenantId"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="用户名" prop="account">
        <el-input
          v-model="queryParams.account"
          placeholder="请输入用户名"
          clearable
          style="width: 240px"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="状态" prop="status">
        <el-select v-model="queryParams.status" placeholder="用户状态" clearable style="width: 240px">
          <el-option
            v-for="dict in statusOptions"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
        >删除</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="userList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="50" align="center" />
      <el-table-column label="编号" align="center" prop="id" />
      <el-table-column label="账户" align="center" prop="account" />
      <el-table-column label="名称" align="center" prop="name" />
      <el-table-column label="电话" align="center" prop="phone" />
      <el-table-column label="状态" align="center" prop="status">
        <template slot-scope="scope">
          <el-switch
            v-model="scope.row.status + ''"
            active-value="0"
            inactive-value="1"
            @change="handleStatusChange(scope.row)"
          ></el-switch>
        </template>
      </el-table-column>
      <el-table-column label="创建时间" align="center" prop="createTime" width="160" sortable="custom" :sort-orders="['descending', 'ascending']">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.createTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" width="220" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
          >修改</el-button>

          <el-dropdown size="mini" @command="(command) => handleCommand(command, scope.row)">
            <span class="el-dropdown-link">
              更多<i class="el-icon-arrow-down el-icon--right"></i>
            </span>
            <el-dropdown-menu slot="dropdown">
              <el-dropdown-item v-if="(scope.row.roleName === '租户' || !isAdmin) && loginUserId !== scope.row.id" icon="el-icon-money">设置价格</el-dropdown-item>
              <el-dropdown-item v-if="(scope.row.roleName === '租户' || !isAdmin) && loginUserId !== scope.row.id" command="addQuota" icon="el-icon-plus">增加配额</el-dropdown-item>
              <el-dropdown-item command="viewUsage" icon="el-icon-data-analysis">查看使用量</el-dropdown-item>
            </el-dropdown-menu>
          </el-dropdown>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改用户对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="600px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item v-if="form.id === undefined" label="账户" prop="account">
          <el-input v-model="form.account" placeholder="请输入账户" maxlength="30" />
        </el-form-item>
        <el-form-item v-if="form.id === undefined" label="密码" prop="password">
          <el-input v-model="form.password" placeholder="请输入用户密码" type="password" maxlength="20" />
        </el-form-item>
        <el-form-item label="名称" prop="name">
          <el-input v-model="form.name" placeholder="请输入用户名称" maxlength="30" />
        </el-form-item>
        <el-form-item label="电话" prop="phone">
          <el-input v-model="form.phone" placeholder="请输入手机号码" maxlength="11" />
        </el-form-item>
        <el-form-item label="邮箱" prop="email">
          <el-input v-model="form.email" placeholder="请输入邮箱" maxlength="50" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>

    <!-- 重置密码对话框 -->
    <el-dialog title="重置密码" :visible.sync="resetPwdOpen" width="400px" append-to-body>
      <el-form ref="resetPwdForm" :model="resetPwdForm" :rules="resetPwdRules" label-width="80px">
        <el-form-item label="新密码" prop="password">
          <el-input v-model="resetPwdForm.password" placeholder="请输入新密码" type="password" />
        </el-form-item>
        <el-form-item label="确认密码" prop="confirmPassword">
          <el-input v-model="resetPwdForm.confirmPassword" placeholder="请确认密码" type="password" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitResetPwd">确 定</el-button>
        <el-button @click="cancelResetPwd">取 消</el-button>
      </div>
    </el-dialog>

    <!-- 设置价格对话框 -->
    <el-dialog title="设置文章价格" :visible.sync="priceOpen" width="400px" append-to-body>
      <el-form ref="priceForm" :model="priceForm" :rules="priceRules" label-width="100px">
        <el-form-item label="文章单价" prop="pricePerArticle">
          <el-input-number v-model="priceForm.pricePerArticle" :precision="2" :step="0.1" :min="0.01"></el-input-number>
        </el-form-item>
        <el-form-item label="币种" prop="currency">
          <el-select v-model="priceForm.currency" placeholder="请选择币种">
            <el-option label="人民币" value="CNY"></el-option>
            <el-option label="美元" value="USD"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="状态">
          <el-radio-group v-model="priceForm.status">
            <el-radio :label="1">启用</el-radio>
            <el-radio :label="0">停用</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitPrice">确 定</el-button>
        <el-button @click="cancelPrice">取 消</el-button>
      </div>
    </el-dialog>

    <!-- 增加配额对话框 -->
    <el-dialog title="增加文章配额" :visible.sync="quotaOpen" width="400px" append-to-body>
      <el-form ref="quotaForm" :model="quotaForm" :rules="quotaRules" label-width="100px">
        <el-form-item label="当前配额">
          <span>{{ currentQuota }}</span>
        </el-form-item>
        <el-form-item label="增加数量" prop="count">
          <el-input-number v-model="quotaForm.count" :min="1" :max="1000"></el-input-number>
        </el-form-item>
        <el-form-item label="增加后配额">
          <span>{{ currentQuota + quotaForm.count }}</span>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitQuota">确 定</el-button>
        <el-button @click="cancelQuota">取 消</el-button>
      </div>
    </el-dialog>

    <!-- 查看使用量对话框 -->
    <el-dialog title="文章使用量" :visible.sync="usageOpen" width="500px" append-to-body>
      <el-card shadow="never" class="usage-card">
        <div class="usage-item">
          <div class="usage-label">已生成文章数：</div>
          <div class="usage-value">{{ usageInfo.totalArticlesGenerated || 0 }}</div>
        </div>
        <div class="usage-item">
          <div class="usage-label">允许生成文章数：</div>
          <div class="usage-value">{{ usageInfo.totalArticlesAllowed || 0 }}</div>
        </div>
        <div class="usage-item">
          <div class="usage-label">使用率：</div>
          <div class="usage-value usage-progress">
            <el-progress :percentage="usagePercentage" :status="usageStatus">
              <span>{{ usageInfo.totalArticlesGenerated || 0 }}/{{ usageInfo.totalArticlesAllowed || 0 }}</span>
            </el-progress>
          </div>
        </div>
        <div class="usage-item">
          <div class="usage-label">最后重置日期：</div>
          <div class="usage-value">{{ parseTime(usageInfo.lastResetDate) }}</div>
        </div>
      </el-card>
      <div slot="footer" class="dialog-footer">
        <el-button @click="usageOpen = false">关 闭</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listTenant } from '@/api/system/tenant'
import { getTenantId } from '@/utils/auth'
import { parseTime } from '@/utils/index'
import store from '@/store'
import {
  listUser,
  listUserByTenant,
  getUser,
  addUser,
  updateUser,
  delUser,
  resetUserPwd,
  changeUserStatus,
  getUserArticleUsage,
  getUserArticlePrice,
  setUserArticlePrice,
  incrementUserArticleAllowance
} from '@/api/system/article-user'
import getters from "../../../store/getters";

export default {
  name: "UserManagement",
  data() {
    const validatePassword = (rule, value, callback) => {
      if (this.form.id !== undefined && !value) {
        callback()
      } else if (!value) {
        callback(new Error('请输入密码'))
      } else {
        callback()
      }
    }
    const validateConfirmPassword = (rule, value, callback) => {
      if (this.form.id !== undefined && !value) {
        callback()
      } else if (!value) {
        callback(new Error('请输入确认密码'))
      } else if (this.form.password !== value) {
        callback(new Error('两次输入的密码不一致'))
      } else {
        callback()
      }
    }
    const validateResetConfirmPassword = (rule, value, callback) => {
      if (!value) {
        callback(new Error('请输入确认密码'))
      } else if (this.resetPwdForm.password !== value) {
        callback(new Error('两次输入的密码不一致'))
      } else {
        callback()
      }
    }
    return {
      // 遮罩层
      loading: false,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 用户表格数据
      userList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 重置密码对话框
      resetPwdOpen: false,
      // 设置价格对话框
      priceOpen: false,
      // 增加配额对话框
      quotaOpen: false,
      // 查看使用量对话框
      usageOpen: false,
      // 状态数据字典
      statusOptions: [
        { value: 1, label: '正常' },
        { value: 0, label: '停用' }
      ],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        tenantId: undefined,
        account: undefined,
        status: undefined
      },
      loginUserId:store.getters.userId,
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        account: [
          { required: true, message: "用户名不能为空", trigger: "blur" },
          { min: 2, max: 20, message: '用户名长度必须介于 2 和 20 之间', trigger: 'blur' }
        ],
        nickname: [
          { required: true, message: "昵称不能为空", trigger: "blur" }
        ],
        password: [
          { required: true, validator: validatePassword, trigger: "blur" },
          { min: 6, message: '密码长度不能小于6个字符', trigger: 'blur' }
        ],
        confirmPassword: [
          { required: true, validator: validateConfirmPassword, trigger: "blur" }
        ],
        email: [
          { type: 'email', message: '请输入正确的邮箱地址', trigger: ['blur', 'change'] }
        ],
        phone: [
          { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号码', trigger: 'blur' }
        ],
        tenantId: [
          { required: true, message: "租户不能为空", trigger: "change" }
        ]
      },
      // 重置密码表单
      resetPwdForm: {
        userId: undefined,
        password: undefined,
        confirmPassword: undefined
      },
      // 重置密码表单校验
      resetPwdRules: {
        password: [
          { required: true, message: "密码不能为空", trigger: "blur" },
          { min: 6, message: '密码长度不能小于6个字符', trigger: 'blur' }
        ],
        confirmPassword: [
          { required: true, validator: validateResetConfirmPassword, trigger: "blur" }
        ]
      },
      // 价格表单
      priceForm: {
        id: undefined,
        tenantId: undefined,
        sysUserId: undefined,
        pricePerArticle: 5.00,
        currency: 'CNY',
        status: 1
      },
      // 价格表单校验
      priceRules: {
        pricePerArticle: [
          { required: true, message: "价格不能为空", trigger: "blur" }
        ],
        currency: [
          { required: true, message: "币种不能为空", trigger: "change" }
        ]
      },
      // 配额表单
      quotaForm: {
        userId: undefined,
        count: 10
      },
      // 配额表单校验
      quotaRules: {
        count: [
          { required: true, message: "增加数量不能为空", trigger: "blur" },
          { type: 'number', min: 1, message: "增加数量必须大于0", trigger: "blur" }
        ]
      },
      // 当前配额
      currentQuota: 0,
      // 使用量信息
      usageInfo: {},
      // 租户选项
      tenantOptions: []
    };
  },
  computed: {
    isAdmin() {
      return getTenantId() == undefined || getTenantId() == null || getTenantId() == ''
    },
    usagePercentage() {
      if (!this.usageInfo || !this.usageInfo.totalArticlesAllowed) return 0
      return Math.min(100, Math.round((this.usageInfo.totalArticlesGenerated / this.usageInfo.totalArticlesAllowed) * 100))
    },
    usageStatus() {
      if (!this.usageInfo) return ''
      const percentage = this.usagePercentage
      if (percentage >= 90) return 'exception'
      if (percentage >= 70) return 'warning'
      return 'success'
    }
  },
  created() {
    this.getList();
    if (this.isAdmin) {
      this.getTenantList();
    } else {
      this.queryParams.tenantId = getTenantId();
    }
  },
  methods: {
    /** 查询用户列表 */
    getList() {
      this.loading = true;
      if (this.isAdmin && !this.queryParams.tenantId) {
        // 管理员查询所有用户
        listUser(this.queryParams).then(response => {
          this.userList = response.rows;
          this.total = response.total;
          this.loading = false;
        });
      } else {
        // 查询指定租户下的用户
        const tenantId = this.isAdmin ? this.queryParams.tenantId : getTenantId();
        this.queryParams.tenantId = tenantId;
        listUserByTenant(this.queryParams).then(response => {
          this.userList = response.rows;
          this.total = response.total;
          this.loading = false;
        });
      }
    },
    /** 获取租户列表 */
    getTenantList() {
      listTenant({ pageNum: 1, pageSize: 100 }).then(response => {
        this.tenantOptions = response.rows;
      });
    },
    // 状态字典翻译
    statusFormat(row, column) {
      return this.selectDictLabel(this.statusOptions, row.status);
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: undefined,
        tenantId: this.isAdmin ? undefined : getTenantId(),
        account: undefined,
        nickname: undefined,
        password: undefined,
        confirmPassword: undefined,
        email: undefined,
        phone: undefined,
        status: 1
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id);
      this.single = selection.length !== 1;
      this.multiple = !selection.length;
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加用户";
    },
    /** 修改按钮操作 */
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids;
      getUser(id).then(response => {
        this.form = response.data;
        this.roleOptions = response.roles;
        this.form.roleIds = response.roleIds;
        this.roleIds = response.roleIds;
        this.open = true;
        this.title = "修改用户";
      });
    },
    /** 提交按钮 */
    submitForm: function() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          this.form.roleIds = this.roleIds;
          if (this.form.id != undefined) {
            updateUser(this.form).then(response => {
              this.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addUser(this.form).then(response => {
              this.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const userIds = row.id || this.ids;
      this.$confirm('是否确认删除用户编号为"' + userIds + '"的数据项？').then(() => {
        this.loading = true;
        return delUser(userIds);
      }).then(() => {
        this.loading = false;
        this.getList();
        this.msgSuccess("删除成功");
      }).catch(() => {
        this.loading = false;
      });
    },
    /** 更多操作 */
    handleCommand(command, row) {
      switch (command) {
        case "resetPwd":
          this.handleResetPwd(row);
          break;
        case "setPrice":
          this.handleSetPrice(row);
          break;
        case "addQuota":
          this.handleAddQuota(row);
          break;
        case "viewUsage":
          this.handleViewUsage(row);
          break;
        default:
          break;
      }
    },
    /** 重置密码按钮操作 */
    handleResetPwd(row) {
      this.resetPwdForm = {
        userId: row.id,
        password: undefined,
        confirmPassword: undefined
      };
      this.resetPwdOpen = true;
    },
    /** 提交重置密码 */
    submitResetPwd() {
      this.$refs["resetPwdForm"].validate(valid => {
        if (valid) {
          resetUserPwd(this.resetPwdForm.userId, this.resetPwdForm.password).then(response => {
            this.msgSuccess("重置成功");
            this.resetPwdOpen = false;
          });
        }
      });
    },
    /** 取消重置密码 */
    cancelResetPwd() {
      this.resetPwdOpen = false;
      this.resetPwdForm = {
        userId: undefined,
        password: undefined,
        confirmPassword: undefined
      };
    },
    /** 设置价格按钮操作 */
    handleSetPrice(row) {
      getUserArticlePrice(row.id).then(response => {
        if (response.code === 200 && response.data) {
          this.priceForm = response.data;
        } else {
          this.priceForm = {
            id: undefined,
            tenantId: row.tenantId,
            sysUserId: row.id,
            pricePerArticle: 5.00,
            currency: 'CNY',
            status: 1
          };
        }
        this.priceOpen = true;
      });
    },
    /** 提交价格设置 */
    submitPrice() {
      this.$refs["priceForm"].validate(valid => {
        if (valid) {
          setUserArticlePrice(this.priceForm).then(response => {
            this.msgSuccess("设置成功");
            this.priceOpen = false;
          });
        }
      });
    },
    /** 取消价格设置 */
    cancelPrice() {
      this.priceOpen = false;
    },
    /** 增加配额按钮操作 */
    handleAddQuota(row) {
      getUserArticleUsage(row.id).then(response => {
        if (response.code === 200 && response.data) {
          this.currentQuota = response.data.totalArticlesAllowed || 0;
        } else {
          this.currentQuota = 0;
        }
        this.quotaForm = {
          userId: row.id,
          count: 10
        };
        this.quotaOpen = true;
      });
    },
    /** 提交增加配额 */
    submitQuota() {
      this.$refs["quotaForm"].validate(valid => {
        if (valid) {
          incrementUserArticleAllowance(this.quotaForm.userId, this.quotaForm.count).then(response => {
            this.msgSuccess("增加配额成功");
            this.quotaOpen = false;
          });
        }
      });
    },
    /** 取消增加配额 */
    cancelQuota() {
      this.quotaOpen = false;
    },
    /** 查看使用量按钮操作 */
    handleViewUsage(row) {
      getUserArticleUsage(row.id).then(response => {
        if (response.code === 200 && response.data) {
          this.usageInfo = response.data;
        } else {
          this.usageInfo = {
            totalArticlesGenerated: 0,
            totalArticlesAllowed: 0,
            lastResetDate: new Date()
          };
        }
        this.usageOpen = true;
      });
    },
    /** 用户状态修改 */
    handleStatusChange(row) {
      let text = row.status === 0 ? "停用" : "启用";
      this.$confirm('确认要"' + text + '""' + row.name + '"用户吗?', "警告", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      }).then(function() {
        return changeUserStatus(row.id, row.status === 0 ? 1 : 0);
      }).then(() => {
        row.status = row.status === 0 ? 1 : 0;
        this.msgSuccess(text + "成功");
      }).catch(function() {

      });
    },
  }
};
</script>

<style scoped>
.usage-card {
  border: 1px solid #ebeef5;
  background-color: #fff;
}

.usage-item {
  display: flex;
  padding: 12px 0;
  border-bottom: 1px solid #ebeef5;
}

.usage-item:last-child {
  border-bottom: none;
}

.usage-label {
  width: 150px;
  color: #606266;
  font-weight: bold;
  text-align: right;
  padding-right: 12px;
}

.usage-value {
  flex: 1;
  color: #303133;
}

.usage-progress {
  width: 100%;
}
</style>
