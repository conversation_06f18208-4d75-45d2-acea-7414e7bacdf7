<template>
  <el-dialog
    title="文章生成配额购买"
    :visible.sync="localVisible"
    width="500px"
    :close-on-click-modal="false"
    append-to-body
  >
    <div class="usage-info">
      <el-alert
        v-if="usageInfo"
        :title="`当前已使用 ${usageInfo.totalArticlesGenerated || 0}/${usageInfo.totalArticlesAllowed || 0} 篇文章`"
        type="info"
        :closable="false"
        show-icon
      >
        <template v-if="usageInfo.totalArticlesGenerated >= usageInfo.totalArticlesAllowed">
          <div class="alert-content">您的文章生成配额已用完，请购买更多配额</div>
        </template>
        <template v-else>
          <div class="alert-content">您还可以生成 {{ usageInfo.totalArticlesAllowed - usageInfo.totalArticlesGenerated }} 篇文章</div>
        </template>
      </el-alert>
    </div>

    <el-form ref="paymentForm" :model="paymentForm" label-width="100px" :rules="rules">
      <el-form-item label="租户" v-if="isAdmin">
        <el-select v-model="paymentForm.tenantId" placeholder="请选择租户" @change="handleTenantChange">
          <el-option
            v-for="item in tenantOptions"
            :key="item.tenantId"
            :label="item.name"
            :value="item.tenantId"
          ></el-option>
        </el-select>
      </el-form-item>

      <el-form-item label="文章单价">
        <span>{{ priceInfo ? priceInfo.pricePerArticle + ' ' + priceInfo.currency : '未设置' }}</span>
      </el-form-item>

      <el-form-item label="购买数量" prop="articleCount">
        <el-input-number
          v-model="paymentForm.articleCount"
          :min="1"
          :max="100"
          controls-position="right"
        ></el-input-number>
      </el-form-item>

      <el-form-item label="支付金额">
        <span>{{ totalAmount }} {{ priceInfo ? priceInfo.currency : 'CNY' }}</span>
      </el-form-item>

      <el-form-item label="支付方式" prop="paymentMethod">
        <el-radio-group v-model="paymentForm.paymentMethod">
          <el-radio :label="'alipay'">支付宝</el-radio>
          <el-radio :label="'wechat'">微信支付</el-radio>
        </el-radio-group>
      </el-form-item>
    </el-form>

    <!-- 微信支付二维码显示区域 -->
    <div v-if="showWechatQrCode && wechatQrCode" class="wechat-qrcode-container">
      <div class="qrcode-title">请使用微信扫描下方二维码进行支付</div>
      <div class="qrcode-wrapper">
        <img :src="getQrCodeImageUrl(wechatQrCode)" alt="微信支付二维码" class="qrcode-image">
      </div>
      <div class="qrcode-tips">扫码支付完成后将自动刷新配额</div>
    </div>

    <div slot="footer" class="dialog-footer">
      <el-button @click="handleClose">取消</el-button>
      <el-button type="primary" @click="handlePayment" :loading="loading" v-if="!showWechatQrCode">确认支付</el-button>
    </div>

    <!-- 支付结果对话框 -->
    <el-dialog
      title="支付结果"
      :visible.sync="paymentResultVisible"
      width="400px"
      append-to-body
      :close-on-click-modal="false"
    >
      <div class="payment-result">
        <i :class="paymentSuccess ? 'el-icon-success success-icon' : 'el-icon-error error-icon'"></i>
        <div class="result-message">{{ paymentResultMessage }}</div>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="handlePaymentResultClose">关闭</el-button>
      </div>
    </el-dialog>
  </el-dialog>
</template>

<script>
import { listTenant } from '@/api/system/tenant'
import { getTenantId } from '@/utils/auth'
import {
  createArticlePaymentOrder,
  getArticlePaymentStatus
} from '@/api/system/article-payment'
import {
  getMyArticlePrice,
  getMyArticleUsage
} from '@/api/system/article-user'

export default {
  name: 'ArticlePayment',
  props: {
    visible: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      localVisible: this.visible,
      loading: false,
      paymentForm: {
        tenantId: undefined,
        sysUserId: undefined,
        articleCount: 10,
        paymentMethod: 'alipay'
      },
      // 微信支付二维码相关
      wechatQrCode: '',
      showWechatQrCode: false,
      rules: {
        articleCount: [
          { required: true, message: '请输入购买数量', trigger: 'blur' },
          { type: 'number', min: 1, max: 100, message: '数量必须在1到100之间', trigger: 'blur' }
        ],
        paymentMethod: [
          { required: true, message: '请选择支付方式', trigger: 'change' }
        ]
      },
      tenantOptions: [],
      priceInfo: null,
      usageInfo: null,
      paymentResultVisible: false,
      paymentSuccess: false,
      paymentResultMessage: '',
      transactionId: null,
      checkPaymentStatusTimer: null
    }
  },
  computed: {
    isAdmin() {
      return getTenantId() == undefined || getTenantId() == null || getTenantId() == ''
    },
    totalAmount() {
      if (!this.priceInfo) return '0.00'
      return (this.paymentForm.articleCount * this.priceInfo.pricePerArticle).toFixed(2)
    }
  },
  watch: {
    visible(val) {
      this.localVisible = val
      if (val) {
        this.init()
      } else {
        this.clearCheckPaymentStatusTimer()
      }
    },
    localVisible(val) {
      if (val !== this.visible) {
        this.$emit('update:visible', val)
      }
    }
  },
  created() {
    // 不再需要在前端存储用户ID，后端会自动获取
  },
  methods: {
    init() {
      this.paymentForm.tenantId = this.isAdmin ? undefined : getTenantId()

      if (this.isAdmin) {
        this.getTenantList()
      } else {
        this.loadUserInfo()
      }
    },
    getTenantList() {
      listTenant({ pageNum: 1, pageSize: 100 }).then(response => {
        this.tenantOptions = response.rows
      })
    },
    handleTenantChange(tenantId) {
      this.paymentForm.tenantId = tenantId
    },
    loadUserInfo() {
      // 获取当前用户文章价格
      getMyArticlePrice().then(response => {
        if (response.code === 200 && response.data) {
          this.priceInfo = response.data
        } else {
          this.priceInfo = null
          this.$message.warning('未设置用户文章价格')
        }
      })

      // 获取当前用户文章使用量
      getMyArticleUsage().then(response => {
        if (response.code === 200 && response.data) {
          this.usageInfo = response.data
        } else {
          this.usageInfo = null
        }
      })
    },

    handleClose() {
      this.clearCheckPaymentStatusTimer()
      this.localVisible = false
      this.showWechatQrCode = false
      this.wechatQrCode = ''
      this.$emit('close')
    },
    handlePayment() {
      this.$refs.paymentForm.validate(valid => {
        if (valid) {
          if (!this.priceInfo) {
            this.$message.warning('未设置文章价格，无法进行支付')
            return
          }

          this.loading = true

          // 创建支付订单
          createArticlePaymentOrder({
            tenantId: this.paymentForm.tenantId,
            // 不需要在前端设置用户ID，后端会自动获取
            articleCount: this.paymentForm.articleCount,
            paymentMethod: this.paymentForm.paymentMethod === 'alipay' ? 1 : 2
          }).then(response => {
            if (response.code === 200 && response.data) {
              const paymentResponse = response.data
              this.transactionId = paymentResponse.transactionId

              // 处理支付参数
              if (paymentResponse.paymentMethod === 1) { // 支付宝
                // 渲染支付宝表单
                const div = document.createElement('div')
                div.innerHTML = paymentResponse.paymentParams
                document.body.appendChild(div)
                document.forms[0].submit()
              } else if (paymentResponse.paymentMethod === 2) { // 微信支付
                // 处理微信支付参数
                try {
                  // 对于PC端，paymentParams直接是二维码链接
                  this.wechatQrCode = paymentResponse.paymentParams
                  this.showWechatQrCode = true

                  // 如果是移动端，可以尝试解析JSON并调用微信支付JS API
                  if (!this.wechatQrCode) {
                    try {
                      const wxParams = JSON.parse(paymentResponse.paymentParams)
                      console.log('微信支付参数:', wxParams)
                      // 这里可以添加移动端微信支付的处理逻辑
                    } catch (jsonError) {
                      console.error('解析微信支付参数失败', jsonError)
                    }
                  }
                } catch (e) {
                  this.$message.error('处理微信支付参数失败')
                  console.error('处理微信支付参数失败', e)
                }
              }

              // 开始轮询支付状态
              this.startCheckPaymentStatus(paymentResponse.transactionId)
            } else {
              this.loading = false
              this.$message.error(response.msg || '创建支付失败')
            }
          }).catch(() => {
            this.loading = false
          })
        }
      })
    },

    startCheckPaymentStatus(transactionId) {
      // 清除可能存在的定时器
      this.clearCheckPaymentStatusTimer()

      // 设置定时器，每3秒检查一次支付状态
      this.checkPaymentStatusTimer = setInterval(() => {
        this.checkPaymentStatus(transactionId)
      }, 3000)
    },
    checkPaymentStatus(transactionId) {
      getArticlePaymentStatus(transactionId).then(response => {
        if (response.code === 200 && response.data) {
          const status = response.data

          // 如果支付成功或失败，停止轮询
          if (status === 1) { // 支付成功
            this.clearCheckPaymentStatusTimer()
            this.showPaymentResult(true, '支付成功！文章生成配额已增加')

            // 刷新用户信息
            this.loadUserInfo()
          } else if (status === 2) { // 支付失败
            this.clearCheckPaymentStatusTimer()
            this.showPaymentResult(false, '支付失败，请重试')
          }
        }
      })
    },
    clearCheckPaymentStatusTimer() {
      if (this.checkPaymentStatusTimer) {
        clearInterval(this.checkPaymentStatusTimer)
        this.checkPaymentStatusTimer = null
      }
    },
    showPaymentResult(success, message) {
      this.loading = false
      this.paymentSuccess = success
      this.paymentResultMessage = message
      this.paymentResultVisible = true
    },
    handlePaymentResultClose() {
      this.paymentResultVisible = false
      if (this.paymentSuccess) {
        this.handleClose()
      }
    },

    // 获取二维码图片URL
    getQrCodeImageUrl(codeUrl) {
      // 使用第三方服务生成二维码图片
      // return `https://api.qrserver.com/v1/create-qr-code/?size=200x200&data=${encodeURIComponent(codeUrl)}`
      return codeUrl
    }
  },
  beforeDestroy() {
    this.clearCheckPaymentStatusTimer()
  }
}
</script>

<style scoped>
.usage-info {
  margin-bottom: 20px;
}

.alert-content {
  margin-top: 5px;
}

.payment-result {
  text-align: center;
  padding: 20px 0;
}

.success-icon {
  font-size: 48px;
  color: #67C23A;
}

.error-icon {
  font-size: 48px;
  color: #F56C6C;
}

.result-message {
  margin-top: 15px;
  font-size: 16px;
}

/* 微信支付二维码相关样式 */
.wechat-qrcode-container {
  text-align: center;
  margin: 20px 0;
  padding: 20px;
  border: 1px solid #e0e0e0;
  border-radius: 4px;
  background-color: #f9f9f9;
}

.qrcode-title {
  font-size: 16px;
  margin-bottom: 15px;
  color: #333;
}

.qrcode-wrapper {
  display: inline-block;
  padding: 10px;
  background: #fff;
  border: 1px solid #ddd;
  border-radius: 4px;
  margin-bottom: 15px;
}

.qrcode-image {
  width: 200px;
  height: 200px;
}

.qrcode-tips {
  font-size: 14px;
  color: #666;
}
</style>
