<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" :inline="true" @submit.native.prevent>
      <el-form-item label="租户" prop="tenantId" v-if="isAdmin">
        <el-select v-model="queryParams.tenantId" placeholder="请选择租户" @change="handleTenantChange">
          <el-option
            v-for="item in tenantOptions"
            :key="item.tenantId"
            :label="item.name"
            :value="item.tenantId"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="关键词" prop="keyword">
        <el-input
          v-model="queryParams.keyword"
          placeholder="请输入关键词"
          clearable
          style="width: 240px"
          @keyup.enter.native="handleSearch"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" @click="handleSearch">搜索</el-button>
        <el-button type="success" icon="el-icon-plus" @click="handleAdd">新增</el-button>
      </el-form-item>
    </el-form>

    <!-- 文章列表 -->
    <el-table v-loading="loading" :data="articleList">
      <el-table-column label="文章标题" align="center" prop="title"/>
      <el-table-column label="链接" align="center" prop="url">
        <template slot-scope="scope">
          <el-link type="primary" :href="scope.row.url" target="_blank">查看原文</el-link>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="primary"
            @click="handleRecreate(scope.row)"
          >二次创作
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 二次创作结果对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="800px" append-to-body>
      <el-form ref="form" :model="form" label-width="80px">
        <el-form-item label="文章标题">
          <el-input v-model="form.title" placeholder="请输入文章标题"/>
        </el-form-item>
        <el-form-item label="封面图片" v-if="form.cozeImageUrl">
          <img :src="form.cozeImageUrl" alt="Coze图片" style="max-width: 100%; height: auto;"/>
        </el-form-item>        <el-form-item label="文章内容">
          <editor v-model="form.content" :min-height="192"/>
        </el-form-item>
        <el-form-item label="租户" prop="tenantId" v-if="isAdmin">
          <el-select v-model="form.tenantId" placeholder="请选择租户" clearable @change="handleTenantChange">
            <el-option
              v-for="item in tenantOptions"
              :key="item.tenantId"
              :label="item.name"
              :value="item.tenantId"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="发布到">
          <el-select v-model="form.wxAppIds" multiple placeholder="请选择公众号">
            <el-option
              v-for="item in officialAccounts"
              :key="item.id"
              :label="item.name"
              :value="item.id"
            ></el-option>
          </el-select>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="handlePublish">发布</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>

    <!-- 二创进度对话框 -->
    <second-creation-progress
      :visible.sync="secondCreationVisible"
      :article-data="currentArticleData"
      :task-id="currentTaskId"
      @complete="handleSecondCreationComplete"
    />

    <!-- 新增文章对话框 -->
    <el-dialog title="新增文章" :visible.sync="addDialogVisible" width="500px" append-to-body>
      <el-form ref="addForm" :model="addForm" label-width="80px">
        <el-form-item label="租户" prop="tenantId" v-if="isAdmin">
          <el-select v-model="addForm.tenantId" placeholder="请选择租户" @change="handleAddTenantChange">
            <el-option
              v-for="item in tenantOptions"
              :key="item.tenantId"
              :label="item.name"
              :value="item.tenantId"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="文章链接" prop="url">
          <el-input v-model="addForm.url" placeholder="请输入文章链接"/>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitAdd">确 定</el-button>
        <el-button @click="cancelAdd">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { searchArticles, publishArticle } from '@/api/system/article'
import { listByTenantId } from '@/api/system/officialAccount'
import { listTenant } from '@/api/system/tenant'
import Editor from '@/components/Editor'
import { getTenantId } from '@/utils/auth'
import SecondCreationProgress from './components/SecondCreationProgress'

export default {
  name: 'ArticleCreation',
  components: {
    Editor,
    SecondCreationProgress
  },
  data() {
    return {
      // 遮罩层
      loading: false,
      // 弹出层标题
      title: '二次创作结果',
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        keyword: undefined
      },
      // 文章列表
      articleList: [],
      // 表单参数
      form: {
        title: undefined,
        content: undefined,
        wxAppIds: [],
        tenantId: undefined,
        cozeImageUrl: undefined
      },
      // 公众号列表
      officialAccounts: [],
      // 租户选项
      tenantOptions: [],
      // 二创进度对话框
      secondCreationVisible: false,
      currentArticleData: null,
      currentTaskId: null,
      // 新增对话框显示状态
      addDialogVisible: false,
      // 新增表单参数
      addForm: {
        url: undefined,
        tenantId: undefined
      }
    }
  },
  computed: {
    isAdmin() {
      return getTenantId() == undefined || getTenantId() == null || getTenantId() == ''
    }
  },
  created() {
    if (this.isAdmin) {
      this.getTenantList()
    } else {
      this.getOfficialAccounts()
    }
  },
  methods: {
    /** 搜索按钮操作 */
    handleSearch() {
      if (!this.queryParams.keyword) {
        this.$message.warning('请输入关键词')
        return
      }
      this.loading = true
      searchArticles(this.queryParams.keyword).then(response => {
        this.articleList = response.data
        this.loading = false
      })
    },
    /** 二次创作按钮操作 */
    handleRecreate(row) {
      const params = {
        url: row.url
      }
      // 如果是管理员且选择了租户，则传入租户ID
      if (this.isAdmin) {
        if (!this.queryParams.tenantId) {
          this.$message.warning('请选择要二创的租户')
          return
        }
        params.tenantId = this.queryParams.tenantId
      } else {
        params.tenantId = getTenantId()
      }
      this.currentArticleData = {
        url: row.url,
        tenantId: params.tenantId
      }
      this.secondCreationVisible = true
    },
    /** 获取租户列表 */
    getTenantList() {
      listTenant({ pageNum: 1, pageSize: 100 }).then(response => {
        this.tenantOptions = response.rows
      })
    },
    /** 获取公众号列表 */
    getOfficialAccounts(tenantId) {
      const id = tenantId
      if (!id) {
        this.officialAccounts = []
        return
      }
      listByTenantId(id).then(response => {
        this.officialAccounts = response.data
      })
    },
    /** 租户变更处理 */
    handleTenantChange(tenantId) {
      this.form.wxAppIds = []
      if (tenantId) {
        this.getOfficialAccounts(tenantId)
      } else {
        this.officialAccounts = []
      }
    },
    /** 发布按钮操作 */
    handlePublish() {
      this.loading = true
      // 直接使用已保存的文章ID发布
      publishArticle(this.form.id, this.form.wxAppIds).then(() => {
        this.msgSuccess('发布成功，请耐心等待审核')
        this.open = false
        this.loading = false
      })
    },
    /** 取消按钮 */
    cancel() {
      this.open = false
      this.reset()
    },
    /** 表单重置 */
    reset() {
      this.form = {
        title: undefined,
        content: undefined,
        wxAppIds: [],
        tenantId: undefined,
        cozeImageUrl: undefined
      }
    },
    // 二创完成处理
    handleSecondCreationComplete(data) {
      console.log('创作完成', data)
      if (data) {
        this.form = {
          title: data.title,
          content: data.content,
          wxAppIds: [],
          tenantId: this.isAdmin ? this.currentArticleData.tenantId : undefined,
          id: data.id,
          cozeImageUrl: data.cozeImageUrl
        }
        this.open = true
      }
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.addDialogVisible = true
      this.addForm = {
        url: undefined,
        tenantId: undefined
      }
    },
    /** 新增租户变更处理 */
    handleAddTenantChange(tenantId) {
      if (tenantId) {
        this.getOfficialAccounts(tenantId)
      } else {
        this.officialAccounts = []
      }
    },
    /** 提交新增 */
    submitAdd() {
      if (!this.addForm.url) {
        this.$message.warning('请输入文章链接')
        return
      }
      if (this.isAdmin && !this.addForm.tenantId) {
        this.$message.warning('请选择租户')
        return
      }

      const params = {
        url: this.addForm.url,
        tenantId: this.isAdmin ? this.addForm.tenantId : getTenantId()
      }

      this.currentArticleData = params
      this.secondCreationVisible = true
      this.addDialogVisible = false
    },
    /** 取消新增 */
    cancelAdd() {
      this.addDialogVisible = false
      this.addForm = {
        url: undefined,
        tenantId: undefined
      }
    }
  }
}
</script>
