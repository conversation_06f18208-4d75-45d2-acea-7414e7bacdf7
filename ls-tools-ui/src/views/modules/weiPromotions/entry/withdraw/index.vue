<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" :inline="true" v-show="showSearch" label-width="130px">
      <el-form-item label="用户编号" prop="userId">
        <el-input v-model="queryParams.userId" placeholder="请输入" clearable size="small"
          @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item label="商户" prop="tenantId" v-show="isShowTenant">
        <el-select v-model="queryParams.tenantId" placeholder="请选择商户" clearable size="small">
          <el-option v-for="item in tenantIdAndTenantNameOptions" :key="item.tenantId" :label="item.tenantName"
            :value="item.tenantId" />
        </el-select>
      </el-form-item>
      <el-form-item label="姓名" prop="realName">
        <el-input v-model="queryParams.realName" placeholder="请输入" clearable size="small"
          @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item label="手机号" prop="phone">
        <el-input v-model="queryParams.phone" placeholder="请输入" clearable size="small"
          @keyup.enter.native="handleQuery" />
      </el-form-item>

      <el-form-item label="提现状态" prop="withdrawStatus">
        <el-select v-model="queryParams.withdrawStatus" placeholder="提现状态" clearable size="small">
          <el-option v-for="dict in withdrawStatusOptions" :key="dict.value" :label="dict.label" :value="dict.value">
          </el-option>
        </el-select>
      </el-form-item>

      <!-- <el-form-item label="金额" prop="amount">
        <el-input v-model="queryParams.amount" placeholder="请输入" clearable size="small"
          @keyup.enter.native="handleQuery" />
      </el-form-item> -->
      <el-form-item label="商家批次单号" prop="outBatchNo">
        <el-input v-model="queryParams.outBatchNo" placeholder="请输入商家批次单号" clearable size="small"
          @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item label="微信批次单号" prop="batchId">
        <el-input v-model="queryParams.batchId" placeholder="请输入微信批次单号" clearable size="small"
          @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item label="批次状态" prop="batchStatus">
        <el-select v-model="queryParams.batchStatus" placeholder="请选择批次状态" clearable size="small">
          <el-option v-for="dict in batchStatusOptions" :key="dict.value" :label="dict.label" :value="dict.value">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="审核打款时间" prop="payTime">
        <el-date-picker clearable size="small" v-model="queryParams.payTime" type="date" value-format="yyyy-MM-dd"
          placeholder="选择审核打款时间
">
        </el-date-picker>
      </el-form-item>
      <el-form-item label="公众号" prop="wxMpId">
        <el-select v-model="queryParams.wxMpId" placeholder="请选择公众号" clearable size="small">
          <el-option v-for="item in officialIdNameOptions" :key="item.id" :label="item.name" :value="item.id">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <!-- <el-col :span="1.5">
        <el-button type="primary" plain icon="el-icon-plus" size="mini" @click="handleAdd"
          v-hasPermi="['wx:withdraw:add']">新增
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="success" plain icon="el-icon-edit" size="mini" :disabled="single" @click="handleUpdate"
          v-hasPermi="['wx:withdraw:edit']">修改
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="danger" plain icon="el-icon-delete" size="mini" :disabled="multiple" @click="handleDelete"
          v-hasPermi="['wx:withdraw:del']">删除
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="warning" plain icon="el-icon-download" size="mini" :loading="exportLoading"
          @click="handleExport" v-hasPermi="['wx:withdraw:export']">导出
        </el-button>
      </el-col> -->
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="withdrawList">
      <!-- <el-table-column type="selection" width="55" align="center" /> -->
      <el-table-column label="编号" align="center" prop="id" />
      <el-table-column label="商户" v-if="isShowTenant" align="center" prop="tenantName">
        <template slot-scope="scope">
          <span v-if="scope.row.tenantName">{{ scope.row.tenantName }}</span>
          <span v-else style="color: red;">未绑定商户</span>
        </template>
      </el-table-column>
      <el-table-column label="真实姓名" align="center" prop="realName" />
      <el-table-column label="手机号" align="center" prop="phone" />
      <el-table-column label="提现状态" align="center" prop="withdrawStatus">
        <template slot-scope="scope">
          <el-tag :type="scope.row.withdrawStatus">{{ scope.row.withdrawStatus }}</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="金额" align="center" prop="amount" />
      <el-table-column label="微信批次单号" align="center" prop="batchId" />
      <el-table-column label="创建时间" align="center" prop="createTime" />
      <el-table-column label="备注" align="center" prop="remark" />
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button :disabled="scope.row.withdrawStatus != '审核中'" slot="reference" type="success"
            @click="openDialogVisible(scope.row.id)">打款</el-button>
          <el-button :disabled="scope.row.withdrawStatus != '审核中'" type="warning"
            @click="openDialogFormVisible(scope.row)" v-hasPermi="['wx:withdraw:edit']">驳回</el-button>

        </template>
      </el-table-column>


    </el-table>

    <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize"
      @pagination="getList" />

    <el-dialog title="提示" :visible.sync="dialogVisible" width="30%">
      <span>请核对打款金额，无误后点击确认？</span>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="handleAudit()">确 定</el-button>
      </span>
    </el-dialog>

    <el-dialog title="驳回" :visible.sync="dialogFormVisible">
      <el-form ref="form" :model="form">
        <el-form-item label="驳回原因" :label-width="formLabelWidth" prop="remark">
          <el-input v-model="form.remark" autocomplete="off"></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogFormVisible = false">取 消</el-button>
        <el-button type="primary" @click="handleRejectWithdraw()">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  listWithdraw,
  getWithdraw,
  delWithdraw,
  addWithdraw,
  updateWithdraw,
  exportWithdraw,
  auditCashWithdraw,
  getAdminCashWithPage,
  rejectWithdraw
} from '@/api/modules/appEntry/withdraw.js'
import { exportExcel } from '@/utils/zipdownload'
import { getAllWeiOfficialIdAndName } from '@/api/modules/entry/official'
import { listTenantIdAndTenantName } from "@/api/system/tenant";
import { getTenantId } from '@/utils/auth'

export default {
  name: 'Withdraw',
  data() {
    return {
      auditId: undefined,
      tenantId: getTenantId(),
      isShowTenant: false,
      tenantIdAndTenantNameOptions: [
        {
          tenantId: undefined,
          tenantName: undefined
        }
      ],
      batchStatusOptions: [
        // 0审核中，1打款中，2已打款，3打款失败
        { label: '待商户确认', value: 'WAIT_PAY' },
        { label: '已受理', value: 'ACCEPTED' },
        { label: '转账中', value: 'PROCESSING' },
        { label: '已完成', value: 'FINISHED' },
        { label: '已关闭', value: 'CLOSED' }
      ],
      officialIdNameOptions: [
        {
          id: undefined,
          name: undefined
        }
      ],
      withdrawStatusOptions: [
        // 0审核中，1打款中，2已打款，3打款失败
        { label: '审核中', value: 0 },
        { label: '打款中', value: 1 },
        { label: '已打款', value: 2 },
        { label: '打款失败', value: 3 }
      ],
      formLabelWidth: '120px',
      dialogTableVisible: false,
      dialogFormVisible: false,
      dialogVisible: false,
      // 遮罩层
      loading: true,
      // 导出遮罩层
      exportLoading: false,
      showSearch: true,
      // 总条数
      total: 0,
      // 表格数据
      withdrawList: [],
      // 弹出层标题
      title: '',
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10
      },
      // 表单参数
      form: {
        remark: undefined
      },
      // 表单校验
      rules: {
        userId: [
          { required: true, message: '不能为空', trigger: 'blur' }
        ],
        withdrawStatus: [
          { required: true, message: '0审核中，1打款中，2已打款，3打款失败不能为空', trigger: 'blur' }
        ],
        amount: [
          { required: true, message: '不能为空', trigger: 'blur' }
        ],
        delFlag: [
          { required: true, message: '不能为空', trigger: 'blur' }
        ]
      }
    }
  },
  created() {
    this.getList()
    this.getAllWeiOfficialIdAndName()
    this.listTenantIdAndTenantName()
  },
  methods: {
    async listTenantIdAndTenantName() {
            console.log("tenantId", this.tenantId);
            if (this.tenantId == undefined || this.tenantId == null || this.tenantId == '') {
                // 没有 代表是管理员 
                this.isShowTenant = true
                let res = await listTenantIdAndTenantName()
                this.tenantIdAndTenantNameOptions = res.data
            } else {
                this.isShowTenant = false
                this.tenantIdAndTenantNameOptions = undefined
            }
            console.log(this.isShowTenant)
        },

    openDialogVisible(id) {
      this.dialogVisible = true
      this.auditId = id
    },
    openDialogFormVisible(item) {
      this.form.id = item.id
      this.dialogFormVisible = true
    },
    async handleAudit() {
      // 将会调用后台的转账
      await auditCashWithdraw(this.auditId)
      this.dialogVisible = false
      this.auditId = undefined
      this.msgSuccess('操作成功')
      this.getList()
    },
    async getAllWeiOfficialIdAndName() {
      let res = await getAllWeiOfficialIdAndName()
      this.officialIdNameOptions = res.data
    },
    /** 查询列表 */
    getList() {
      this.loading = true
      getAdminCashWithPage(this.queryParams).then(response => {
        this.withdrawList = response.rows
        this.total = response.total
        this.loading = false
      })
    },
    async handleRejectWithdraw() {
      this.dialogFormVisible = false
      await rejectWithdraw(this.form)
      this.msgSuccess('驳回成功')
      this.getList()
      // 字段重置
      this.$refs['form'].resetFields()
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1
      this.getList()
    },
    // /** 重置按钮操作 */
    resetQuery() {
      this.$refs.queryParams.resetFields()
    },
    /** 导出按钮操作 */
    handleExport() {
      const queryParams = this.queryParams
      this.$confirm('是否确认导出所有数据项?', '警告', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.exportLoading = true
        // 开始导出
        exportExcel('/wx/cash_withdraw/export', this.queryParams)
        this.exportLoading = false
      }).catch(() => {
      })
    }
  }
}
</script>
