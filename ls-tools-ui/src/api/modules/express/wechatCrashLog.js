import request from '@/utils/request'

const baseUrl = '/express/wechat-crash-log/';

// 查询微信崩溃日志列表
export function listWechatCrashLog(query) {
  return request({
    url: baseUrl + 'list',
    method: 'get',
    params: query
  })
}

// 查询微信崩溃日志详细
export function getWechatCrashLog(id) {
  return request({
    url: baseUrl + id,
    method: 'get'
  })
}

// 新增微信崩溃日志
export function addWechatCrashLog(data) {
  return request({
    url: baseUrl,
    method: 'post',
    data: data
  })
}

// 修改微信崩溃日志
export function updateWechatCrashLog(data) {
  return request({
    url: baseUrl,
    method: 'put',
    data: data
  })
}

// 删除微信崩溃日志
export function delWechatCrashLog(id) {
  return request({
    url: baseUrl + id,
    method: 'delete'
  })
}

// 根据IP地址查询崩溃日志
export function getWechatCrashLogByIp(ip) {
  return request({
    url: baseUrl + 'by-ip/' + ip,
    method: 'get'
  })
}

// 根据错误关键字查询崩溃日志
export function getWechatCrashLogByError(keyword) {
  return request({
    url: baseUrl + 'by-error/' + keyword,
    method: 'get'
  })
}
