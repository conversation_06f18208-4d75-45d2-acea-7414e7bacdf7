import request from '@/utils/request'

// 查询当前用户文章列表
export function listMyArticles(articleQuery) {
  return request({
    url: '/system/tenant-article/my',
    method: 'get',
    params:articleQuery
  })
}

// 查询当前用户文章发布记录列表
export function listMyArticlePublish(publishQuery) {
  return request({
    url: '/system/tenant-article-publish/my',
    method: 'get',
    params:publishQuery
  })
}

// 查询当前用户公众号列表
export function listMyOfficialAccounts(accountQuery) {
  return request({
    url: '/system/officialAccount/my',
    method: 'get',
    params:accountQuery
  })
}

// 获取当前用户的所有公众号（不分页）
export function getMyOfficialAccounts() {
  return request({
    url: '/system/officialAccount/listMy',
    method: 'get'
  })
}

// 获取文章详情
export function getArticleDetail(articleId) {
  return request({
    url: `/system/tenant-article/${articleId}`,
    method: 'get'
  })
}

// 获取发布记录详情
export function getPublishDetail(publishId) {
  return request({
    url: `/system/tenant-article-publish/${publishId}`,
    method: 'get'
  })
}

// 获取公众号详情
export function getOfficialAccountDetail(accountId) {
  return request({
    url: `/system/officialAccount/${accountId}`,
    method: 'get'
  })
}

// 新增公众号
export function addOfficialAccount(data) {
  return request({
    url: '/system/officialAccount',
    method: 'post',
    data: data
  })
}

// 修改公众号
export function updateOfficialAccount(data) {
  return request({
    url: '/system/officialAccount',
    method: 'put',
    data: data
  })
}

// 删除公众号
export function delOfficialAccount(accountId) {
  return request({
    url: `/system/officialAccount/${accountId}`,
    method: 'delete'
  })
}

// 发布文章到公众号
export function publishArticle(articleId, wxAppIds) {
  return request({
    url: `/system/tenant-article/publish/${articleId}`,
    method: 'post',
    data: wxAppIds
  })
}
