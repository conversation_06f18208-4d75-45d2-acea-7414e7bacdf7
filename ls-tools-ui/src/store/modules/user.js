import { login, logout, getInfo } from '@/api/login'
import { getToken, setToken, removeToken ,getTenantId ,removeWeiToken,setTenantId,getTenantName,setTenantName,removeTenantId } from '@/utils/auth'
import websocket from "../../utils/webSocket";
const user = {
  state: {
    token: getToken(),
    tenantId: getTenantId(),
    tenantName: getTenantName(),
    name: '',
    avatar: '',
    roles: [],
    permissions: [],
    userId: null
  },

  mutations: {
    SET_TOKEN: (state, token) => {
      state.token = token
    },
    SET_TENANT_ID: (state, tenantId) => {
      state.tenantId = tenantId
    },
    SET_TENANT_NAME: (state, tenantName) => {
      state.tenantName = tenantName
    },
    SET_NAME: (state, name) => {
      state.name = name
    },
    SET_AVATAR: (state, avatar) => {
      state.avatar = avatar
    },
    SET_ROLES: (state, roles) => {
      state.roles = roles
    },
    SET_PERMISSIONS: (state, permissions) => {
      state.permissions = permissions
    },
    SET_USER_ID: (state, userId) => {
      state.userId = userId
    }
  },

  actions: {
    // 登录
    Login({ commit }, userInfo) {
      const username = userInfo.username.trim()
      const password = userInfo.password
      return new Promise((resolve, reject) => {
        login(username, password).then(res => {
          setToken(res.data.tokenValue)
          setTenantId(res.data.tenantId)
          commit('SET_TOKEN', res.data.tokenValue)
          commit('SET_TENANT_ID', res.data.tenantId)
          // websocket.Init(res.data.tokenValue)
          resolve()
        }).catch(error => {
          reject(error)
        })
      })
    },

    // 获取用户信息
    GetInfo({ commit, state }) {
      return new Promise((resolve, reject) => {
        getInfo().then(res => {
          const user = res.user
          const avatar = !user.avatar ? require("@/assets/images/profile.jpg") : user.avatar;
          if (res.roles && res.roles.length > 0) { // 验证返回的roles是否是一个非空数组
            commit('SET_ROLES', res.roles)
            commit('SET_PERMISSIONS', res.permissions)
          } else {
            commit('SET_ROLES', ['ROLE_DEFAULT'])
          }
          commit('SET_NAME', user.userName)
          commit('SET_TENANT_NAME', user.tenant.name)
          commit('SET_AVATAR', avatar)
          commit('SET_USER_ID', user.id)
          resolve(res)
        }).catch(error => {
          reject(error)
        })
      })
    },

    // 退出系统
    LogOut({ commit, state }) {
      return new Promise((resolve, reject) => {
        logout(state.token).then(() => {
          commit('SET_TOKEN', '')
          commit('SET_ROLES', [])
          commit('SET_PERMISSIONS', [])
          commit('SET_TENANT_ID', '')
          commit('SET_TENANT_NAME', '')
          commit('SET_USER_ID', null)
          removeToken()
          removeTenantId()
          // websocket.onClose()
          resolve()
        }).catch(error => {
          reject(error)
        })
      })
    },

    // 前端 登出
    FedLogOut({ commit }) {
      return new Promise(resolve => {
        commit('SET_TOKEN', '')
        commit('SET_TENANT_ID', '')
        commit('SET_TENANT_NAME', '')
        commit('SET_USER_ID', null)
        removeToken()
        removeWeiToken()
        removeTenantId()
        resolve()
      })
    }
  }
}

export default user
