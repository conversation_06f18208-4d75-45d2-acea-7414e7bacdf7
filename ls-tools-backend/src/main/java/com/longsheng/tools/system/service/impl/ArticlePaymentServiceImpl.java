package com.longsheng.tools.system.service.impl;

import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.lang.UUID;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.longsheng.tools.common.payment.WechatPayService;
import com.longsheng.tools.common.payment.dto.WechatPcPayDTO;
import com.longsheng.tools.common.utils.RES;
import com.longsheng.tools.common.payment.dto.PayOrderRespDTO;
import com.longsheng.tools.common.enums.PayOrderStatusRespEnum;
import com.github.binarywang.wxpay.exception.WxPayException;
import com.longsheng.tools.system.constant.PaymentConstants;
import com.longsheng.tools.system.entity.TenantArticleTransaction;
import com.longsheng.tools.system.entity.UserArticlePrice;
import com.longsheng.tools.system.mapper.TenantArticleTransactionMapper;
import com.longsheng.tools.system.service.ArticlePaymentService;
import com.longsheng.tools.system.service.UserArticlePriceService;
import com.longsheng.tools.system.service.UserArticleUsageService;
import com.longsheng.tools.system.vo.ArticlePaymentRequest;
import com.longsheng.tools.system.vo.ArticlePaymentResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 文章支付服务实现类
 */
@Slf4j
@Service
public class ArticlePaymentServiceImpl implements ArticlePaymentService {

    @Resource
    private UserArticlePriceService userArticlePriceService;

    @Resource
    private UserArticleUsageService userArticleUsageService;

    @Resource
    private TenantArticleTransactionMapper tenantArticleTransactionMapper;

    @Resource
    private WechatPayService wechatPayService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public RES createPayment(ArticlePaymentRequest request) {
        // 如果请求中没有用户ID，使用当前登录用户ID
        if (request.getSysUserId() == null) {
            request.setSysUserId(StpUtil.getLoginIdAsLong());
        }

        // 获取用户价格
        UserArticlePrice userPrice = userArticlePriceService.getBySysUserId(request.getSysUserId());
        if (userPrice == null || userPrice.getStatus() != 1) {
            return RES.no("未找到用户价格配置或价格未启用");
        }

        BigDecimal pricePerArticle = userPrice.getPricePerArticle();

        // 2. 计算总金额
        BigDecimal amount = pricePerArticle.multiply(new BigDecimal(request.getArticleCount()));

        // 3. 生成交易号
        String transactionNo = generateTransactionNo();

        // 4. 创建交易记录
        TenantArticleTransaction transaction = TenantArticleTransaction.builder()
                .tenantId(request.getTenantId())
                .sysUserId(request.getSysUserId())
                .amount(amount)
                .articleCount(request.getArticleCount())
                .transactionDate(new Date())
                .paymentStatus(PaymentConstants.PAYMENT_STATUS_PENDING)
                .transactionNo(transactionNo)
                .paymentMethod(request.getPaymentMethod())
                .createTime(new Date())
                .updateTime(new Date())
                .build();

        tenantArticleTransactionMapper.insert(transaction);

        // 5. 根据支付方式生成支付参数
        String paymentParams = generatePaymentParams(transaction);

        // 6. 构建响应
        ArticlePaymentResponse response = ArticlePaymentResponse.builder()
                .transactionId(transaction.getId())
                .transactionNo(transaction.getTransactionNo())
                .amount(transaction.getAmount())
                .articleCount(transaction.getArticleCount())
                .paymentMethod(transaction.getPaymentMethod())
                .paymentStatus(transaction.getPaymentStatus())
                .paymentParams(paymentParams)
                .build();

        return RES.ok(response);
    }

    @Override
    public String handleAlipayCallback(String params) {
        // 解析支付宝回调参数
        log.info("收到支付宝回调: {}", params);

        // 这里应该解析支付宝回调参数，验证签名等
        // 由于是示例代码，这里简化处理

        // 假设我们从回调中提取了交易号和支付状态
        String outTradeNo = "模拟交易号"; // 应该从params中提取
        String tradeStatus = "TRADE_SUCCESS"; // 应该从params中提取

        // 根据交易号查询交易记录
        TenantArticleTransaction transaction = getByTransactionNo(outTradeNo);
        if (transaction == null) {
            log.error("未找到交易记录: {}", outTradeNo);
            return "fail";
        }

        // 更新交易状态
        if ("TRADE_SUCCESS".equals(tradeStatus)) {
            transaction.setPaymentStatus(PaymentConstants.PAYMENT_STATUS_SUCCESS);
            transaction.setPaymentProof(outTradeNo);
            transaction.setUpdateTime(new Date());

            if (updateTransaction(transaction)) {
                // 增加用户文章配额
                userArticleUsageService.incrementArticleAllowance(transaction.getSysUserId(), transaction.getArticleCount(), 1);
                return "success";
            }
        }

        return "fail";
    }

    @Override
    public String handleWechatPayCallback(String xmlData) {
        log.info("收到微信支付回调 (ArticlePaymentService): {}", xmlData);

        try {
            // Parse the WeChat notification
            PayOrderRespDTO payOrderRespDTO = wechatPayService.doParseOrderNotify(xmlData);
            String outTradeNo = payOrderRespDTO.getOutTradeNo();
            log.info("Parsed WeChat callback (ArticlePaymentService) - outTradeNo: {}, status: {}", outTradeNo, payOrderRespDTO.getStatus());

            // According to PaymentRecordServiceImpl, the outTradeNo from DTO is the one to use.
            // TenantArticleTransaction uses transactionNo which should match outTradeNo from Wechat.

            TenantArticleTransaction transaction = getByTransactionNo(outTradeNo);
            if (transaction == null) {
                log.error("未找到交易记录 (ArticlePaymentService): {}", outTradeNo);
                return "<xml><return_code><![CDATA[FAIL]]></return_code><return_msg><![CDATA[交易不存在]]></return_msg></xml>";
            }

            // Check if already processed
            if (PaymentConstants.PAYMENT_STATUS_SUCCESS.equals(transaction.getPaymentStatus())) {
                log.info("交易 {} 已成功处理，忽略重复回调 (ArticlePaymentService)", outTradeNo);
                return "<xml><return_code><![CDATA[SUCCESS]]></return_code><return_msg><![CDATA[OK]]></return_msg></xml>";
            }

            if (payOrderRespDTO.getStatus() == PayOrderStatusRespEnum.SUCCESS) {
                transaction.setPaymentStatus(PaymentConstants.PAYMENT_STATUS_SUCCESS);
                // Storing the original outTradeNo (which is our transactionNo) as payment proof.
                // If channelOrderNo is needed, it's in payOrderRespDTO.getChannelOrderNo()
                transaction.setPaymentProof(outTradeNo); 
                transaction.setUpdateTime(new Date());

                if (updateTransaction(transaction)) {
                    // Increment user article allowance
                    userArticleUsageService.incrementArticleAllowance(transaction.getSysUserId(), transaction.getArticleCount(), 1);
                    log.info("交易 {} 成功处理并更新文章配额 (ArticlePaymentService)", outTradeNo);
                    return "<xml><return_code><![CDATA[SUCCESS]]></return_code><return_msg><![CDATA[OK]]></return_msg></xml>";
                } else {
                    log.error("更新交易记录失败 (ArticlePaymentService): {}", outTradeNo);
                    return "<xml><return_code><![CDATA[FAIL]]></return_code><return_msg><![CDATA[更新交易失败]]></return_msg></xml>";
                }
            } else {
                log.warn("微信支付回调状态非成功 (ArticlePaymentService) outTradeNo: {}, status: {}", outTradeNo, payOrderRespDTO.getStatus());
                // Optionally update transaction status to FAILED or other relevant status
                // transaction.setPaymentStatus(PaymentConstants.PAYMENT_STATUS_FAILED);
                // updateTransaction(transaction);
                return "<xml><return_code><![CDATA[FAIL]]></return_code><return_msg><![CDATA[支付未成功]]></return_msg></xml>";
            }
        } catch (WxPayException e) {
            log.error("解析微信支付回调失败 (WxPayException in ArticlePaymentService): {}", e.getMessage(), e);
            return "<xml><return_code><![CDATA[FAIL]]></return_code><return_msg><![CDATA[回调处理失败]]></return_msg></xml>";
        } catch (Exception e) {
            log.error("处理微信支付回调时发生未知错误 (ArticlePaymentService): {}", e.getMessage(), e);
            return "<xml><return_code><![CDATA[FAIL]]></return_code><return_msg><![CDATA[系统异常]]></return_msg></xml>";
        }
    }

    @Override
    public RES getPaymentStatus(Long transactionId) {
        TenantArticleTransaction transaction = tenantArticleTransactionMapper.selectById(transactionId);
        if (transaction == null) {
            return RES.no("交易不存在");
        }
        return RES.ok(transaction.getPaymentStatus());
    }

    @Override
    public TenantArticleTransaction getByTransactionNo(String transactionNo) {
        if (StrUtil.isBlank(transactionNo)) {
            return null;
        }

        LambdaQueryWrapper<TenantArticleTransaction> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(TenantArticleTransaction::getTransactionNo, transactionNo);

        return tenantArticleTransactionMapper.selectOne(queryWrapper);
    }

    @Override
    public boolean updateTransaction(TenantArticleTransaction transaction) {
        if (transaction == null || transaction.getId() == null) {
            return false;
        }

        int rows = tenantArticleTransactionMapper.updateById(transaction);
        return rows > 0;
    }

    /**
     * 生成交易号
     */
    private String generateTransactionNo() {
        return UUID.fastUUID().toString(true);
    }

    /**
     * 根据支付方式生成支付参数
     */
    private String generatePaymentParams(TenantArticleTransaction transaction) {
        if (transaction.getPaymentMethod() == PaymentConstants.PAYMENT_METHOD_ALIPAY) {
            // 这里应该调用支付宝SDK生成支付表单
            // 由于是示例代码，这里返回模拟数据
            return "<form id='alipayForm' action='https://openapi.alipay.com/gateway.do' method='POST'>" +
                    "<input type='hidden' name='app_id' value='2021000000000000'/>" +
                    "<input type='hidden' name='method' value='alipay.trade.page.pay'/>" +
                    "<input type='hidden' name='format' value='JSON'/>" +
                    "<input type='hidden' name='charset' value='utf-8'/>" +
                    "<input type='hidden' name='sign_type' value='RSA2'/>" +
                    "<input type='hidden' name='timestamp' value='2023-01-01 00:00:00'/>" +
                    "<input type='hidden' name='version' value='1.0'/>" +
                    "<input type='hidden' name='notify_url' value='http://your-domain.com/api/payment/alipay/notify'/>" +
                    "<input type='hidden' name='return_url' value='http://your-domain.com/payment/result'/>" +
                    "<input type='hidden' name='biz_content' value='{\"out_trade_no\":\"" + transaction.getTransactionNo() + "\",\"total_amount\":\"" + transaction.getAmount() + "\",\"subject\":\"文章生成服务\",\"product_code\":\"FAST_INSTANT_TRADE_PAY\"}'/>" +
                    "<input type='hidden' name='sign' value='模拟签名'/>" +
                    "</form>";
        } else if (transaction.getPaymentMethod() == PaymentConstants.PAYMENT_METHOD_WECHAT) {
            try {
                // 调用微信PC支付接口，生成二维码链接
                WechatPcPayDTO pcPayDTO = wechatPayService.wechatPcPay(
                        transaction.getTransactionNo(),
                        transaction.getAmount(),
                        "文章生成服务"
                );

                // 返回二维码链接
                return pcPayDTO.getCodeUrl();
            } catch (Exception e) {
                log.error("生成微信支付参数失败", e);
                return null;
            }
        }

        return null;
    }
}
