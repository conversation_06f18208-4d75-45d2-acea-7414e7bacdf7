package com.longsheng.tools.system.controller;

import com.longsheng.tools.common.utils.RES;
import com.longsheng.tools.system.service.ArticlePaymentService;

import com.longsheng.tools.system.vo.ArticlePaymentRequest;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import java.util.HashMap;
import java.util.Map;

/**
 * 文章支付Controller
 *
 * <AUTHOR>
 * @date 2024-01-19
 */
@RestController
@RequestMapping("/system/article-payment")
public class ArticlePaymentController {

    @Resource
    private ArticlePaymentService articlePaymentService;

    /**
     * 创建支付订单
     */
    @PostMapping("/create")
    public RES createPayment(@Valid @RequestBody ArticlePaymentRequest request) {
        return articlePaymentService.createPayment(request);
    }

    /**
     * 查询支付状态
     */
    @GetMapping("/status/{transactionId}")
    public RES getPaymentStatus(@PathVariable Long transactionId) {
        return articlePaymentService.getPaymentStatus(transactionId);
    }

    /**
     * 支付宝回调接口
     */
    @PostMapping("/alipay/notify")
    public String alipayNotify(HttpServletRequest request) {
        // 获取支付宝POST过来的反馈信息
        Map<String, String> params = new HashMap<>();
        Map<String, String[]> requestParams = request.getParameterMap();
        for (String name : requestParams.keySet()) {
            String[] values = requestParams.get(name);
            String valueStr = "";
            for (int i = 0; i < values.length; i++) {
                valueStr = (i == values.length - 1) ? valueStr + values[i] : valueStr + values[i] + ",";
            }
            params.put(name, valueStr);
        }

        return articlePaymentService.handleAlipayCallback(params.toString());
    }

    /**
     * 微信支付回调接口
     */
    // @PostMapping("/wechat/notify")
    // public String wechatPayNotify(HttpServletRequest request) {
    //     // 获取微信POST过来的XML数据
    //     StringBuilder sb = new StringBuilder();
    //     try (BufferedReader reader = request.getReader()) {
    //         String line;
    //         while ((line = reader.readLine()) != null) {
    //             sb.append(line);
    //         }
    //     } catch (IOException e) {
    //         return "<xml><return_code><![CDATA[FAIL]]></return_code><return_msg><![CDATA[读取数据失败]]></return_msg></xml>";
    //     }
    //
    //     return articlePaymentService.handleWechatPayCallback(sb.toString());
    // }
}
