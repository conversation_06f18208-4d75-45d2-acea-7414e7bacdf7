package com.longsheng.tools.system.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.longsheng.tools.common.utils.RES;
import com.longsheng.tools.system.entity.UserArticleUsage;
import com.longsheng.tools.system.vo.TableInfo;

/**
 * 用户文章使用量Service接口
 *
 * <AUTHOR>
 * @date 2024-01-20
 */
public interface UserArticleUsageService extends IService<UserArticleUsage> {

    /**
     * 查询用户文章使用量列表
     *
     * @param userArticleUsage 查询条件
     * @param isExport 是否导出，0-否，1-是
     * @return 分页结果
     */
    TableInfo list(UserArticleUsage userArticleUsage, int isExport);

    /**
     * 获取用户文章使用量详情
     */
    RES getUserArticleUsage(Long id);

    /**
     * 获取用户的文章使用量
     */
    UserArticleUsage userArticleUsage(Long sysUserId);

    /**
     * 新增用户文章使用量
     */
    RES add(UserArticleUsage userArticleUsage);

    /**
     * 修改用户文章使用量
     */
    RES update(UserArticleUsage userArticleUsage);

    /**
     * 删除用户文章使用量
     */
    RES delete(Long[] ids);

    /**
     * 检查用户是否有足够的文章生成配额
     *
     * @param sysUserId 系统用户ID
     * @param count 需要生成的文章数量
     * @return 是否有足够的配额
     */
    boolean checkArticleQuota(Long sysUserId, int count);

    /**
     * 增加用户已生成的文章数量
     *
     * @param sysUserId 系统用户ID
     * @param count 增加的数量
     * @return 操作结果
     */
    RES incrementArticleCount(Long sysUserId, int count);

    /**
     * 增加用户允许生成的文章数量
     *
     * @param sysUserId 系统用户ID
     * @param count 增加的数量
     * @param type 充值类型：0-手动分配，1-支付充值
     * @return 操作结果
     */
    RES incrementArticleAllowance(Long sysUserId, int count, int type);

    /**
     * 创建用户文章使用量记录
     *
     * @param sysUserId 系统用户ID
     * @return 创建的使用量记录
     */
    UserArticleUsage createUserArticleUsage(Long sysUserId);
}
