package com.longsheng.tools.system.service.impl;

import cn.dev33.satoken.stp.StpUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.longsheng.tools.common.tenant.TenantContextHolder;
import com.longsheng.tools.common.utils.PageUtils;
import com.longsheng.tools.common.utils.RES;
import com.longsheng.tools.system.constant.PaymentConstants;
import com.longsheng.tools.system.entity.TenantArticleTransaction;
import com.longsheng.tools.system.entity.UserArticlePrice;
import com.longsheng.tools.system.mapper.TenantArticleTransactionMapper;
import com.longsheng.tools.system.service.TenantArticleTransactionService;
import com.longsheng.tools.system.service.UserArticlePriceService;
import com.longsheng.tools.system.service.UserArticleUsageService;
import com.longsheng.tools.system.vo.PageVO;
import com.longsheng.tools.system.vo.TableInfo;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Arrays;
import java.util.Date;

/**
 * 租户文章交易记录Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-01-19
 */
@Service
public class TenantArticleTransactionServiceImpl extends ServiceImpl<TenantArticleTransactionMapper, TenantArticleTransaction> implements TenantArticleTransactionService {

    @Resource
    private UserArticlePriceService userArticlePriceService;

    @Resource
    private UserArticleUsageService userArticleUsageService;

    @Override
    public TableInfo list(TenantArticleTransaction tenantArticleTransaction, int isExport) {
        LambdaQueryWrapper<TenantArticleTransaction> queryWrapper = new LambdaQueryWrapper<>();

        // 构建查询条件
        if (tenantArticleTransaction.getTenantId() != null) {
            queryWrapper.eq(TenantArticleTransaction::getTenantId, tenantArticleTransaction.getTenantId());
        }

        if (tenantArticleTransaction.getPaymentStatus() != null) {
            queryWrapper.eq(TenantArticleTransaction::getPaymentStatus, tenantArticleTransaction.getPaymentStatus());
        }
        if (TenantContextHolder.isTenantUser()) {
            queryWrapper.eq(TenantArticleTransaction::getSysUserId, StpUtil.getLoginIdAsLong());
        }

        // 默认排序
        queryWrapper.orderByDesc(TenantArticleTransaction::getCreateTime);

        PageVO pageVO = PageUtils.getPageVO();
        // 分页查询
        Page<TenantArticleTransaction> page = new Page<>(pageVO.getPageNum(), pageVO.getPageSize());
        Page<TenantArticleTransaction> pageResult = this.page(page, queryWrapper);

        return TableInfo.ok(pageResult);
    }

    @Override
    public RES getTenantArticleTransaction(Long id) {
        TenantArticleTransaction tenantArticleTransaction = this.getById(id);
        return RES.ok(tenantArticleTransaction);
    }

    @Override
    public RES add(TenantArticleTransaction tenantArticleTransaction) {
        // 设置默认值
        tenantArticleTransaction.setCreateTime(new Date());
        tenantArticleTransaction.setUpdateTime(new Date());

        // 如果未设置交易日期，默认为当前时间
        if (tenantArticleTransaction.getTransactionDate() == null) {
            tenantArticleTransaction.setTransactionDate(new Date());
        }

        // 如果未设置支付状态，默认为待支付
        if (tenantArticleTransaction.getPaymentStatus() == null) {
            tenantArticleTransaction.setPaymentStatus(0);
        }

        this.save(tenantArticleTransaction);
        return RES.ok();
    }

    @Override
    public RES update(TenantArticleTransaction tenantArticleTransaction) {
        tenantArticleTransaction.setUpdateTime(new Date());
        this.updateById(tenantArticleTransaction);
        return RES.ok();
    }

    @Override
    public RES delete(Long[] ids) {
        this.removeByIds(Arrays.asList(ids));
        return RES.ok();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public RES createTransaction(Long tenantId, int articleCount) {
        // 获取租户的价格配置
//        TenantArticlePrice price = tenantArticlePriceService.getByTenantId(tenantId);
        long userId = StpUtil.getLoginIdAsLong();
        UserArticlePrice price = userArticlePriceService.getBySysUserId(userId);
        if (price == null) {
            return RES.no("未找到租户的价格配置");
        }

        // 计算总金额
        BigDecimal amount = price.getPricePerArticle().multiply(new BigDecimal(articleCount));

        // 生成交易号
        String transactionNo = "ART" + System.currentTimeMillis();

        // 创建交易记录
        TenantArticleTransaction transaction = TenantArticleTransaction.builder()
                .sysUserId(StpUtil.getLoginIdAsLong())
                .tenantId(tenantId)
                .amount(amount)
                .articleCount(articleCount)
                .transactionDate(new Date())
                .paymentStatus(PaymentConstants.PAYMENT_STATUS_PENDING)
                .transactionNo(transactionNo)
                .createTime(new Date())
                .updateTime(new Date())
                .build();

        this.save(transaction);

        return RES.ok(transaction);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public RES updateTransactionStatus(Long transactionId, int status) {
        TenantArticleTransaction transaction = this.getById(transactionId);
        if (transaction == null) {
            return RES.no("交易记录不存在");
        }

        // 更新交易状态
        transaction.setPaymentStatus(status);
        transaction.setUpdateTime(new Date());

        this.updateById(transaction);

        // 如果支付成功，增加租户的文章配额
        if (status == PaymentConstants.PAYMENT_STATUS_SUCCESS) {
            userArticleUsageService.incrementArticleAllowance(transaction.getTenantId(), transaction.getArticleCount(),2);
        }

        return RES.ok();
    }
}
