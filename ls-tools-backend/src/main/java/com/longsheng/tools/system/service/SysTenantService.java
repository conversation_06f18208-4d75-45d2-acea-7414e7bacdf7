package com.longsheng.tools.system.service;

import com.longsheng.tools.common.elementui.LabelVo;
import com.longsheng.tools.system.entity.SysTenant;
import com.longsheng.tools.system.vo.ListTenantIdAndTenantNameVo;
import com.longsheng.tools.system.vo.TableInfo;
import com.longsheng.tools.common.utils.RES;
import com.baomidou.mybatisplus.extension.service.IService;
import com.longsheng.tools.system.vo.TenantVo;

import java.util.List;

/**
 * Service接口
 *
 * <AUTHOR>
 * @date 2024-09-19
 */
public interface SysTenantService extends IService<SysTenant> {

    /**
    * 查询SysTenant
    *
    * @param isExport 是否导出，0-否，1-是
    * @return
    */
    TableInfo list(SysTenant sysTenant, int isExport);

    /**
     * 获取SysTenant
     */
    SysTenant getSysTenant(Long id);

    TenantVo getTenantById(Long id);

    /**
     * 新增SysTenant
     */
    RES add(SysTenant sysTenant);

    /**
     * 修改SysTenant
     */
    RES update(SysTenant sysTenant);

    /**
     * 删除SysTenant
     */
    RES delete(Long[] ids);

    void validTenant(Long id);

    List<Long> getTenantIdList();

    List<ListTenantIdAndTenantNameVo> listTenantIdAndTenantName();

//    SysTenant getTenantByAppId(String appid);

    List<LabelVo<Long>> getTenantIdAndNameList();
}
