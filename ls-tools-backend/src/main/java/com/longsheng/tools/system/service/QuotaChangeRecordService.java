package com.longsheng.tools.system.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.longsheng.tools.common.utils.RES;
import com.longsheng.tools.system.entity.QuotaChangeRecord;
import com.longsheng.tools.system.vo.TableInfo;

/**
 * 配额变更记录Service接口
 *
 * <AUTHOR>
 */
public interface QuotaChangeRecordService extends IService<QuotaChangeRecord> {

    /**
     * 查询配额变更记录列表
     *
     * @param quotaChangeRecord 配额变更记录
     * @param isExport 是否导出，0-否，1-是
     * @return 分页结果
     */
    TableInfo list(QuotaChangeRecord quotaChangeRecord, int isExport);

    /**
     * 获取配额变更记录详情
     *
     * @param id 配额变更记录ID
     * @return 配额变更记录信息
     */
    RES getQuotaChangeRecord(Long id);

    /**
     * 新增配额变更记录
     *
     * @param quotaChangeRecord 配额变更记录信息
     * @return 结果
     */
    RES add(QuotaChangeRecord quotaChangeRecord);

    /**
     * 修改配额变更记录
     *
     * @param quotaChangeRecord 配额变更记录信息
     * @return 结果
     */
    RES update(QuotaChangeRecord quotaChangeRecord);

    /**
     * 删除配额变更记录
     *
     * @param ids 需要删除的配额变更记录ID数组
     * @return 结果
     */
    RES delete(Long[] ids);
}
