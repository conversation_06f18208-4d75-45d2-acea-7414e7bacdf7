package com.longsheng.tools.system.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.longsheng.tools.common.base.TenantBaseDO;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.Date;

/**
 * 配额变更记录对象
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("quota_change_record")
public class QuotaChangeRecord extends TenantBaseDO<QuotaChangeRecord> {

    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 变更数量
     */
    @TableField("count")
    private Integer count;

    /**
     * 分配方式：0-手动，1-充值，2-商户扣减
     */
    @TableField("allocation_method")
    private Integer allocationMethod;

    /**
     * 用户ID
     */
    @TableField("user_id")
    private Integer userId;

    /**
     * 用户名称
     */
    @TableField("user_name")
    private String userName;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private Date createTime;

    /**
     * 创建人
     */
    @TableField("create_user")
    private String createUser;

    /**
     * 接收用户ID
     */
    @TableField("to_user")
    private Integer toUser;

    /**
     * 删除标志（0代表存在 1代表删除）
     */
    @TableField("del_flag")
    @TableLogic
    private Integer delFlag;

}
