package com.longsheng.tools.system.service.impl;

import cn.dev33.satoken.stp.StpUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.longsheng.tools.common.tenant.TenantContextHolder;
import com.longsheng.tools.common.utils.PageUtils;
import com.longsheng.tools.common.utils.RES;
import com.longsheng.tools.system.entity.QuotaChangeRecord;
import com.longsheng.tools.system.entity.SysUserInfo;
import com.longsheng.tools.system.entity.UserArticleUsage;
import com.longsheng.tools.system.enums.AllowanceType;
import com.longsheng.tools.system.mapper.UserArticleUsageMapper;
import com.longsheng.tools.system.service.LoginService;
import com.longsheng.tools.system.service.QuotaChangeRecordService;
import com.longsheng.tools.system.service.SysTenantService;
import com.longsheng.tools.system.service.UserArticleUsageService;
import com.longsheng.tools.system.vo.PageVO;
import com.longsheng.tools.system.vo.TableInfo;
import com.longsheng.tools.system.vo.TenantVo;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.Date;
import java.util.List;

/**
 * 用户文章使用量Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-01-20
 */
@Service
public class UserArticleUsageServiceImpl extends ServiceImpl<UserArticleUsageMapper, UserArticleUsage> implements UserArticleUsageService {

    @Resource
    private QuotaChangeRecordService quotaChangeRecordService;
    @Resource
    private LoginService loginService;
    @Resource
    private SysTenantService sysTenantService;

    @Override
    public TableInfo list(UserArticleUsage userArticleUsage, int isExport) {
        LambdaQueryWrapper<UserArticleUsage> queryWrapper = new LambdaQueryWrapper<>();

        // 构建查询条件
        if (userArticleUsage.getTenantId() != null) {
            queryWrapper.eq(UserArticleUsage::getTenantId, userArticleUsage.getTenantId());
        }

        if (TenantContextHolder.isTenantUser()) {
            queryWrapper.eq(UserArticleUsage::getSysUserId, StpUtil.getLoginIdAsLong());
        }

        // 默认排序
        queryWrapper.orderByDesc(UserArticleUsage::getCreateTime);
        PageVO pageVO = PageUtils.getPageVO();
        // 分页查询
        Page<UserArticleUsage> page = new Page<>(pageVO.getPageNum(), pageVO.getPageSize());
        Page<UserArticleUsage> pageResult = this.page(page, queryWrapper);

        return TableInfo.ok(pageResult);
    }

    @Override
    public RES getUserArticleUsage(Long id) {
        UserArticleUsage userArticleUsage = this.getById(id);
        return RES.ok(userArticleUsage);
    }

    @Override
    public UserArticleUsage userArticleUsage(Long sysUserId) {
        LambdaQueryWrapper<UserArticleUsage> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(UserArticleUsage::getSysUserId, sysUserId);

        List<UserArticleUsage> list = this.list(queryWrapper);

        // 如果不存在，则创建一个新的使用量记录
        if (list.isEmpty()) {
            // 调用专门的创建方法，确保在可写事务中执行
            return createUserArticleUsage(sysUserId);
        }

        return list.get(0);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public UserArticleUsage createUserArticleUsage(Long sysUserId) {
        // 再次检查是否存在，防止并发情况下重复创建
        LambdaQueryWrapper<UserArticleUsage> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(UserArticleUsage::getSysUserId, sysUserId);
        List<UserArticleUsage> list = this.list(queryWrapper);
        if (!list.isEmpty()) {
            return list.get(0);
        }
        SysUserInfo userInfo = loginService.getUserInfo(sysUserId.intValue());
        // 创建新的使用量记录
        UserArticleUsage usage = new UserArticleUsage();
        usage.setSysUserId(sysUserId);
        usage.setTenantId(userInfo.getTenantId());
        usage.setTotalArticlesGenerated(0);
        usage.setTotalArticlesAllowed(0);
        usage.setLastResetDate(new Date());
        usage.setCreateTime(new Date());
        usage.setUpdateTime(new Date());
        this.save(usage);
        return usage;
    }

    @Override
    public RES add(UserArticleUsage userArticleUsage) {
        // 检查是否已存在该用户的使用量记录
        LambdaQueryWrapper<UserArticleUsage> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(UserArticleUsage::getSysUserId, userArticleUsage.getSysUserId());

        long count = this.count(queryWrapper);
        if (count > 0) {
            return RES.no("该用户已存在使用量记录");
        }

        // 设置默认值
        userArticleUsage.setCreateTime(new Date());
        userArticleUsage.setUpdateTime(new Date());

        // 如果未设置已生成文章数，默认为0
        if (userArticleUsage.getTotalArticlesGenerated() == null) {
            userArticleUsage.setTotalArticlesGenerated(0);
        }

        // 如果未设置允许生成文章数，默认为0
        if (userArticleUsage.getTotalArticlesAllowed() == null) {
            userArticleUsage.setTotalArticlesAllowed(0);
        }

        // 如果未设置最后重置日期，默认为当前时间
        if (userArticleUsage.getLastResetDate() == null) {
            userArticleUsage.setLastResetDate(new Date());
        }

        this.save(userArticleUsage);
        return RES.ok();
    }

    @Override
    public RES update(UserArticleUsage userArticleUsage) {
        userArticleUsage.setUpdateTime(new Date());
        this.updateById(userArticleUsage);
        return RES.ok();
    }

    @Override
    public RES delete(Long[] ids) {
        this.removeByIds(Arrays.asList(ids));
        return RES.ok();
    }

    @Override
    public boolean checkArticleQuota(Long sysUserId, int count) {
        // 获取用户的文章使用量
        UserArticleUsage usage = userArticleUsage(sysUserId);

        // 检查是否有足够的配额
        return usage.getTotalArticlesGenerated() + count <= usage.getTotalArticlesAllowed();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public RES incrementArticleCount(Long sysUserId, int count) {
        // 获取用户的文章使用量，如果不存在会创建一个新的
        UserArticleUsage usage = userArticleUsage(sysUserId);

        // 增加已生成文章数
        usage.setTotalArticlesGenerated(usage.getTotalArticlesGenerated() + count);
        usage.setUpdateTime(new Date());

        this.updateById(usage);
        return RES.ok();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public RES incrementArticleAllowance(Long sysUserId, int count, int type) {
        // 参数验证
        RES validationResult = validateIncrementAllowanceParams(sysUserId, count, type);
        if (!validationResult.isSuccess()) {
            return validationResult;
        }

        // 获取用户的文章使用量，如果不存在会创建一个新的
        UserArticleUsage usage = userArticleUsage(sysUserId);
        AllowanceType allowanceType = AllowanceType.fromCode(type);

        // 根据充值类型处理不同的业务逻辑
        RES processResult;
        switch (allowanceType) {
            case MANUAL_ALLOCATION:
                processResult = processManualAllocation(sysUserId, count, usage);
                break;
            case PAYMENT_RECHARGE:
                processResult = processPaymentRecharge(sysUserId, count, usage);
                break;
            default:
                return RES.no("不支持的充值类型: " + type);
        }

        if (!processResult.isSuccess()) {
            return processResult;
        }

        // 增加用户允许生成文章数
        return increaseUserAllowance(usage, count);
    }

    /**
     * 参数验证
     */
    private RES validateIncrementAllowanceParams(Long sysUserId, int count, int type) {
        if (sysUserId == null || sysUserId <= 0) {
            return RES.no("用户ID不能为空或无效");
        }
        if (count <= 0) {
            return RES.no("充值数量必须大于0");
        }
        try {
            AllowanceType.fromCode(type);
        } catch (IllegalArgumentException e) {
            return RES.no(e.getMessage());
        }
        return RES.ok();
    }

    /**
     * 处理租户手动分配配额
     */
    private RES processManualAllocation(Long sysUserId, int count, UserArticleUsage usage) {
        long currentUserId = StpUtil.getLoginIdAsLong();

        // 权限检查：不能给自己分配配额
        if (currentUserId == sysUserId) {
            return RES.no("无权限分配配额");
        }

        // 权限检查：必须是租户管理员
        if (!TenantContextHolder.isTenantAdmin()) {
            return RES.no("只有租户管理员才能手动分配配额");
        }

        Long tenantId = TenantContextHolder.getTenantId();
        if (tenantId == null) {
            return RES.no("无法获取租户信息");
        }

        // 获取租户配额并校验
        UserArticleUsage tenantUsage = userArticleUsage(currentUserId);
        RES quotaCheckResult = checkTenantQuota(tenantUsage, count, "租户配额不足,请先充值");
        if (!quotaCheckResult.isSuccess()) {
            return quotaCheckResult;
        }

        // 扣减租户配额并记录
        deductTenantQuota(tenantUsage, count);
        saveQuotaChangeRecord(currentUserId, sysUserId, count, 2, tenantId, String.valueOf(currentUserId));
        saveQuotaChangeRecord(sysUserId, null, count, 0, tenantId, String.valueOf(currentUserId));

        return RES.ok();
    }

    /**
     * 处理用户支付充值
     */
    private RES processPaymentRecharge(Long sysUserId, int count, UserArticleUsage usage) {
        Long tenantId = usage.getTenantId();
        if (tenantId == null) {
            return RES.no("无法获取用户租户信息");
        }

        TenantVo tenant = sysTenantService.getTenantById(tenantId);
        if (tenant == null || tenant.getContactUserId() == null) {
            return RES.no("租户信息不完整");
        }

        Long tenantUserId = tenant.getContactUserId();
        UserArticleUsage tenantUsage = userArticleUsage(tenantUserId);

        // 校验租户配额
        RES quotaCheckResult = checkTenantQuota(tenantUsage, count, "租户配额不足,请联系租户充值");
        if (!quotaCheckResult.isSuccess()) {
            return quotaCheckResult;
        }

        // 扣减租户配额并记录
        deductTenantQuota(tenantUsage, count);
        saveQuotaChangeRecord(tenantUserId, sysUserId, count, 2, tenantId, String.valueOf(sysUserId));
        saveQuotaChangeRecord(sysUserId, null, count, 1, tenantId, String.valueOf(sysUserId));

        return RES.ok();
    }

    /**
     * 检查租户配额是否充足
     */
    private RES checkTenantQuota(UserArticleUsage tenantUsage, int count, String errorMessage) {
        if (tenantUsage.getTotalArticlesAllowed() < tenantUsage.getTotalArticlesGenerated() + count) {
            return RES.no(errorMessage);
        }
        return RES.ok();
    }

    /**
     * 扣减租户配额
     */
    private void deductTenantQuota(UserArticleUsage tenantUsage, int count) {
        tenantUsage.setTotalArticlesAllowed(tenantUsage.getTotalArticlesAllowed() - count);
        tenantUsage.setUpdateTime(new Date());
        this.updateById(tenantUsage);
    }

    /**
     * 保存配额变更记录
     */
    private void saveQuotaChangeRecord(Long userId, Long toUserId, int count, int allocationMethod,
                                     Long tenantId, String createUser) {
        QuotaChangeRecord record = new QuotaChangeRecord();
        record.setCreateTime(new Date());
        record.setCount(allocationMethod == 2 ? count * -1 : count); // 租户扣减记录为负数
        record.setAllocationMethod(allocationMethod);
        record.setUserId(userId.intValue());
        record.setCreateUser(createUser);
        if (toUserId != null) {
            record.setToUser(toUserId.intValue());
        }
        record.setTenantId(tenantId);
        quotaChangeRecordService.save(record);
    }

    /**
     * 增加用户允许生成文章数
     */
    private RES increaseUserAllowance(UserArticleUsage usage, int count) {
        usage.setTotalArticlesAllowed(usage.getTotalArticlesAllowed() + count);
        usage.setUpdateTime(new Date());
        this.updateById(usage);
        return RES.ok();
    }
}
