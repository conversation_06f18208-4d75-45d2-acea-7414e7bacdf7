package com.longsheng.tools.system.mapper;

import com.longsheng.tools.system.vo.SysMenuVO;
import com.longsheng.tools.system.entity.SysPermissionInfo;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;

import java.util.List;

/**
 * <p>
 * 菜单权限表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 */
public interface SysPermissionInfoMapper extends BaseMapper<SysPermissionInfo> {

    List<SysMenuVO> listSysMenuVOAll();

    List<SysMenuVO> listSysMenuVOAllByUserId(Integer userId);
}
