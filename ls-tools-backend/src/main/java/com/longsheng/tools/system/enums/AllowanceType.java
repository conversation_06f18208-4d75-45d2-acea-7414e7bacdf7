package com.longsheng.tools.system.enums;

/**
 * 配额充值类型枚举
 *
 * <AUTHOR>
 */
public enum AllowanceType {
    
    /**
     * 租户给用户手动充值
     */
    MANUAL_ALLOCATION(0, "手动分配"),
    
    /**
     * 用户支付充值
     */
    PAYMENT_RECHARGE(1, "支付充值");
    
    private final int code;
    private final String description;
    
    AllowanceType(int code, String description) {
        this.code = code;
        this.description = description;
    }
    
    public int getCode() {
        return code;
    }
    
    public String getDescription() {
        return description;
    }
    
    /**
     * 根据code获取枚举
     */
    public static AllowanceType fromCode(int code) {
        for (AllowanceType type : values()) {
            if (type.code == code) {
                return type;
            }
        }
        throw new IllegalArgumentException("未知的充值类型: " + code);
    }
}
