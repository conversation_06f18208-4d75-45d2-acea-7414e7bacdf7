package com.longsheng.tools.system.service;

import com.longsheng.tools.system.vo.TableInfo;
import com.longsheng.tools.common.utils.RES;
import com.longsheng.tools.system.entity.SysDict;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * <p>
 * 字典管理 服务类
 * </p>
 *
 * <AUTHOR>
 */
public interface SysDictService extends IService<SysDict> {

    TableInfo list(SysDict sysDict);

    RES listSelect();

    RES getSysDict(Integer id);

    RES add(SysDict sysDict);

    RES update(SysDict sysDict);

    RES delete(Integer[] dictCodes);

    RES optionselect();
}
