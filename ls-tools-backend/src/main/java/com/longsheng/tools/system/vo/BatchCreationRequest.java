package com.longsheng.tools.system.vo;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 批量创作请求参数
 */
public class BatchCreationRequest {

    /**
     * 租户ID
     */
    private Long tenantId;

    /**
     * 系统用户ID
     */
    private Long sysUserId;

    /**
     * 批量创作任务列表
     */
    @NotEmpty(message = "创作任务不能为空")
    private List<BatchCreationTask> tasks;

    public Long getTenantId() {
        return tenantId;
    }

    public void setTenantId(Long tenantId) {
        this.tenantId = tenantId;
    }

    public Long getSysUserId() {
        return sysUserId;
    }

    public void setSysUserId(Long sysUserId) {
        this.sysUserId = sysUserId;
    }

    public List<BatchCreationTask> getTasks() {
        return tasks;
    }

    public void setTasks(List<BatchCreationTask> tasks) {
        this.tasks = tasks;
    }
}