package com.longsheng.tools.system.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.longsheng.tools.common.utils.DateUtils;
import com.longsheng.tools.common.utils.PageUtils;
import com.longsheng.tools.common.utils.RES;
import com.longsheng.tools.system.entity.QuotaChangeRecord;
import com.longsheng.tools.system.mapper.QuotaChangeRecordMapper;
import com.longsheng.tools.system.service.QuotaChangeRecordService;
import com.longsheng.tools.system.vo.PageVO;
import com.longsheng.tools.system.vo.TableInfo;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.Arrays;
import java.util.Date;
import java.util.List;

/**
 * 配额变更记录Service实现
 *
 * <AUTHOR>
 */
@Service
public class QuotaChangeRecordServiceImpl extends ServiceImpl<QuotaChangeRecordMapper, QuotaChangeRecord> implements QuotaChangeRecordService {

    /**
     * 查询配额变更记录列表
     *
     * @param quotaChangeRecord 配额变更记录
     * @param isExport 是否导出，0-否，1-是
     * @return 分页结果
     */
    @Override
    public TableInfo list(QuotaChangeRecord quotaChangeRecord, int isExport) {
        LambdaQueryWrapper<QuotaChangeRecord> queryWrapper = new LambdaQueryWrapper<>();

        // 设置查询条件
        if (quotaChangeRecord.getUserId() != null) {
            queryWrapper.eq(QuotaChangeRecord::getUserId, quotaChangeRecord.getUserId());
        }

        if (quotaChangeRecord.getToUser() != null) {
            queryWrapper.eq(QuotaChangeRecord::getToUser, quotaChangeRecord.getToUser());
        }

        if (quotaChangeRecord.getAllocationMethod() != null) {
            queryWrapper.eq(QuotaChangeRecord::getAllocationMethod, quotaChangeRecord.getAllocationMethod());
        }

        if (!StringUtils.isEmpty(quotaChangeRecord.getUserName())) {
            queryWrapper.like(QuotaChangeRecord::getUserName, quotaChangeRecord.getUserName());
        }

        // 按创建时间降序排序
        queryWrapper.orderByDesc(QuotaChangeRecord::getCreateTime);
        PageVO pageVO = PageUtils.getPageVO();
        Page<QuotaChangeRecord> page = this.page(new Page<>(pageVO.getPageNum(), pageVO.getPageSize()), queryWrapper);
        return TableInfo.ok(page);
    }

    /**
     * 获取配额变更记录详情
     *
     * @param id 配额变更记录ID
     * @return 配额变更记录信息
     */
    @Override
    public RES getQuotaChangeRecord(Long id) {
        QuotaChangeRecord quotaChangeRecord = this.getById(id);
        if (quotaChangeRecord == null) {
            return RES.no("配额变更记录不存在");
        }
        return RES.ok(quotaChangeRecord);
    }

    /**
     * 新增配额变更记录
     *
     * @param quotaChangeRecord 配额变更记录信息
     * @return 结果
     */
    @Override
    public RES add(QuotaChangeRecord quotaChangeRecord) {
        // 设置创建时间
        if (quotaChangeRecord.getCreateTime() == null) {
            quotaChangeRecord.setCreateTime(new Date());
        }

        boolean result = this.save(quotaChangeRecord);
        if (result) {
            return RES.ok();
        } else {
            return RES.no("新增配额变更记录失败");
        }
    }

    /**
     * 修改配额变更记录
     *
     * @param quotaChangeRecord 配额变更记录信息
     * @return 结果
     */
    @Override
    public RES update(QuotaChangeRecord quotaChangeRecord) {
        if (quotaChangeRecord.getId() == null) {
            return RES.no("配额变更记录ID不能为空");
        }

        boolean result = this.updateById(quotaChangeRecord);
        if (result) {
            return RES.ok();
        } else {
            return RES.no("修改配额变更记录失败");
        }
    }

    /**
     * 删除配额变更记录
     *
     * @param ids 需要删除的配额变更记录ID数组
     * @return 结果
     */
    @Override
    public RES delete(Long[] ids) {
        boolean result = this.removeByIds(Arrays.asList(ids));
        if (result) {
            return RES.ok();
        } else {
            return RES.no("删除配额变更记录失败");
        }
    }
}
