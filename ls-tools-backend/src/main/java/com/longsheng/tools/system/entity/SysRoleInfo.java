package com.longsheng.tools.system.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 角色信息表
 * </p>
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("sys_role_info")
public class SysRoleInfo extends Model<SysRoleInfo> {


    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    @TableField("name")
    private String name;

    @TableField("sign")
    private String sign;

    @TableField("remark")
    private String remark;

    @TableField("status")
    private Integer status;

    @TableField("sort")
    private Integer sort;

    @TableField("create_user")
    private Integer createUser;

    @TableField("create_time")
    private Date createTime;

    @TableField("update_user")
    private Integer updateUser;

    @TableField("update_time")
    private Date updateTime;

    @TableField("del_flag")
    @TableLogic
    private Integer delFlag;


    @Override
    public Serializable pkVal() {
        return this.id;
    }

}
