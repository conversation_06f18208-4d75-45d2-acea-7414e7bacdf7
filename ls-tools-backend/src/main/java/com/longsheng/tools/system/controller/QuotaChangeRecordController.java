package com.longsheng.tools.system.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.longsheng.tools.common.annotation.SysLog;
import com.longsheng.tools.common.enums.BusinessType;
import com.longsheng.tools.common.excel.ExportExcelUtil;
import com.longsheng.tools.common.utils.RES;
import com.longsheng.tools.system.entity.QuotaChangeRecord;
import com.longsheng.tools.system.service.QuotaChangeRecordService;
import com.longsheng.tools.system.vo.TableInfo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 配额变更记录Controller
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/system/quota-change-record")
public class QuotaChangeRecordController {

    @Autowired
    private QuotaChangeRecordService quotaChangeRecordService;

    /**
     * 查询配额变更记录列表
     */
    @SaCheckPermission("system:quota:list")
    @GetMapping("/list")
    public TableInfo list(QuotaChangeRecord quotaChangeRecord) {
        return quotaChangeRecordService.list(quotaChangeRecord, 0);
    }

    /**
     * 导出配额变更记录列表
     */
    @SysLog(title = "配额变更记录", type = BusinessType.EXPORT)
    @SaCheckPermission("system:quota:export")
    @GetMapping("/export")
    public void export(QuotaChangeRecord quotaChangeRecord) {
        List<?> list = quotaChangeRecordService.list(quotaChangeRecord, 1).getRows();
        ExportExcelUtil.exportExcel(list, QuotaChangeRecord.class, "配额变更记录表", "配额变更记录统计");
    }

    /**
     * 获取配额变更记录详情
     */
    @SaCheckPermission("system:quota:query")
    @GetMapping(value = "/{id}")
    public RES getQuotaChangeRecord(@PathVariable("id") Long id) {
        return quotaChangeRecordService.getQuotaChangeRecord(id);
    }

    /**
     * 新增配额变更记录
     */
    @SysLog(title = "配额变更记录", type = BusinessType.INSERT)
    @SaCheckPermission("system:quota:add")
    @PostMapping
    public RES add(@RequestBody QuotaChangeRecord quotaChangeRecord) {
        return quotaChangeRecordService.add(quotaChangeRecord);
    }

    /**
     * 修改配额变更记录
     */
    @SysLog(title = "配额变更记录", type = BusinessType.UPDATE)
    @SaCheckPermission("system:quota:edit")
    @PutMapping
    public RES edit(@RequestBody QuotaChangeRecord quotaChangeRecord) {
        return quotaChangeRecordService.update(quotaChangeRecord);
    }

    /**
     * 删除配额变更记录
     */
    @SysLog(title = "配额变更记录", type = BusinessType.DELETE)
    @SaCheckPermission("system:quota:del")
    @DeleteMapping("/{ids}")
    public RES remove(@PathVariable Long[] ids) {
        return quotaChangeRecordService.delete(ids);
    }
}
