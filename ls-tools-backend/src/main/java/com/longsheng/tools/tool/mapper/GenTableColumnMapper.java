package com.longsheng.tools.tool.mapper;

import com.baomidou.mybatisplus.annotation.InterceptorIgnore;
import com.longsheng.tools.tool.entity.GenTableColumn;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;

import java.util.List;

/**
 * <p>
 * 代码生成业务表字段 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 */
@InterceptorIgnore(tenantLine = "true")
public interface GenTableColumnMapper extends BaseMapper<GenTableColumn> {

    List<GenTableColumn> selectDbTableColumnsByName(String tableName);
}
