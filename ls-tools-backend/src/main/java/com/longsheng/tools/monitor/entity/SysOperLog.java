package com.longsheng.tools.monitor.entity;

import cn.afterturn.easypoi.excel.annotation.Excel;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.longsheng.tools.common.base.TenantBaseDO;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.Date;

/**
 * 操作日志记录对象 sys_oper_log
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("sys_oper_log")
public class SysOperLog extends TenantBaseDO<SysOperLog> {


    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @Excel(name = "模块标题", width = 15)
    @TableField("title")
    private String title;

    @Excel(name = "业务类型", width = 15, dict = "oper_type")
    @TableField("type")
    private Integer type;

    @Excel(name = "方法名称", width = 30)
    @TableField("fun")
    private String fun;

    @Excel(name = "请求方式", width = 15)
    @TableField("method")
    private String method;

    @Excel(name = "请求URL", width = 30)
    @TableField("url")
    private String url;

    @Excel(name = "主机地址", width = 15)
    @TableField("ip")
    private String ip;

    @Excel(name = "操作地点", width = 30)
    @TableField("location")
    private String location;

    @Excel(name = "请求参数", width = 30)
    @TableField("param")
    private String param;

    @Excel(name = "返回参数", width = 30)
    @TableField("result")
    private String result;

    @Excel(name = "操作状态", width = 15, dict = "is_success")
    @TableField("status")
    private Integer status;

    @Excel(name = "错误消息", width = 15)
    @TableField("error_msg")
    private String errorMsg;

    @Excel(name = "耗时(ms)", width = 15)
    @TableField("spend_time")
    private Long spendTime;

    @Excel(name = "操作人员", width = 15)
    @TableField("oper_name")
    private String operName;

    @Excel(name = "操作时间", width = 30, format = "yyyy-MM-dd HH:mm:ss")
    @TableField("create_time")
    private Date createTime;


    @Override
    public Serializable pkVal() {
        return this.id;
    }
}
