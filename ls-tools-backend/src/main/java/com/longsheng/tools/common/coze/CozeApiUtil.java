package com.longsheng.tools.common.coze;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.longsheng.tools.common.exception.CustomException;
import com.longsheng.tools.common.utils.HttpUtils;
import com.longsheng.tools.common.utils.SpringContextUtils;
import com.longsheng.tools.system.service.SysDictDetailService;
import com.longsheng.tools.system.service.impl.SysDictDetailServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;

import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
public class CozeApiUtil {

    private static final String APP_ID = "7469538024519401499";
    public static final String REQUEST_URL = "https://api.coze.cn/v1/workflow/run";

    private static JSONObject sendRequest(String workflowId, JSONObject parameters) throws CustomException {
        Map<String, String> headers = new HashMap<>();
        headers.put("Content-Type", "application/json");
        SysDictDetailService detailService = SpringContextUtils.getBean(SysDictDetailService.class);
        String token = detailService.getDictDataByTypeAndValue("COZE", "SECRET_TOKEN");
        headers.put("Authorization", "Bearer " + token);

        JSONObject body = new JSONObject();
        body.put("workflow_id", workflowId);
        body.put("app_id", APP_ID);
        body.put("parameters", parameters);

        try {
            String response = HttpUtils.sendJsonPost(REQUEST_URL, body.toJSONString(), headers);
            JSONObject jsonObject = JSONObject.parseObject(response);
            if (jsonObject.getInteger("code") == 0) {
                return jsonObject.getJSONObject("data");
            } else {
                throw new CustomException("请求失败：" + jsonObject.toJSONString());
            }
        } catch (Exception e) {
            log.error("请求失败：" + e.getMessage(), e);
        }
        throw new CustomException("请求失败");
    }

    public static List<ToutiaoNewsDto> searchHotNews(String keyword) {
        JSONObject parameters = new JSONObject();
        parameters.put("key_word", keyword);

        try {
            JSONObject data = sendRequest(CozeApi.SEARCH_ARTICLE.getWorkflowId(), parameters);
            JSONArray output = data.getJSONArray("data");
            if (CollectionUtils.isEmpty(output)) {
                output = data.getJSONArray("output");
            }
            return output.toJavaList(ToutiaoNewsDto.class);
        } catch (CustomException e) {
            log.error("搜索文章失败：" + e.getMessage(), e);
        }
        return Collections.emptyList();
    }

    public static SecondCreationDto secondCreation(String url) {
        JSONObject parameters = new JSONObject();
        parameters.put("url", url);

        try {
            JSONObject data = sendRequest(CozeApi.SECOND_CREATION.getWorkflowId(), parameters);
            String image = data.getString("image");
            JSONObject output = data.getJSONObject("output");
            output.put("cozeImageUrl", image);
            return output.toJavaObject(SecondCreationDto.class);
        } catch (CustomException e) {
            log.error("二次创作失败：" + e.getMessage(), e);
        }
        throw new CustomException("二次创作失败");
    }

    public static List<String> generateContentImage(String prompt) {
        int count = prompt.split("placeholder.jpg").length - 1;
        JSONObject parameters = new JSONObject();
        parameters.put("input", prompt);
        parameters.put("count", count);

        try {
            JSONObject data = sendRequest(CozeApi.GENERATE_IMAGE.getWorkflowId(), parameters);
            JSONArray output = data.getJSONArray("output");
            if (CollectionUtils.isNotEmpty(output)) {
                return output.toJavaList(String.class);
            }
            return Collections.emptyList();
        } catch (CustomException e) {
            log.error("生成图片失败：" + e.getMessage(), e);
        }
        throw new CustomException("生成图片失败");
    }

    public static ArticleDto readUrl(String url) {
        JSONObject parameters = new JSONObject();
        parameters.put("url", url);
        try {
            JSONObject data = sendRequest(CozeApi.READ_URL.getWorkflowId(), parameters);
            JSONObject output = data.getJSONObject("output");
            return output.toJavaObject(ArticleDto.class);
        } catch (CustomException e) {
            log.error("读取链接失败：" + e.getMessage(), e);
            throw new CustomException("读取链接失败");
        }
    }

    public static String generateCoverImage(String title, String content) {
        JSONObject parameters = new JSONObject();
        parameters.put("title", title);
        parameters.put("content", content);
        try {
            JSONObject data = sendRequest(CozeApi.GENERATE_COVER_IMAGE.getWorkflowId(), parameters);
            return data.getString("output");
        } catch (CustomException e) {
            log.error("生成封面图片失败：" + e.getMessage(), e);
            throw new CustomException("生成封面图片失败");
        }
    }

    public static ArticleDto rewriteArticle(String title, String content) {
        JSONObject parameters = new JSONObject();
        parameters.put("title", title);
        parameters.put("content", content);
        try {
            JSONObject data = sendRequest(CozeApi.REWRITE_ARTICLE.getWorkflowId(), parameters);
            JSONObject output = data.getJSONObject("output");
            return output.toJavaObject(ArticleDto.class);
        } catch (CustomException e) {
            log.error("重写文章失败：" + e.getMessage(), e);
        }
        throw new CustomException("重写文章失败");
    }

}
