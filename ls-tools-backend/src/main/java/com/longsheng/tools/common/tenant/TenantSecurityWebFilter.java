package com.longsheng.tools.common.tenant;

import cn.dev33.satoken.stp.StpInterface;
import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import com.longsheng.tools.common.base.TenantBaseDO;
import com.longsheng.tools.common.config.GlobalExceptionHandler;
import com.longsheng.tools.common.enums.StatusEnums;
import com.longsheng.tools.common.config.TenantProperties;
import com.longsheng.tools.common.enums.UserTypeEnum;
import com.longsheng.tools.common.interfaces.IUser;
import com.longsheng.tools.common.satoken.SaTokenStpInterfaceImpl;
import com.longsheng.tools.common.utils.RES;
import com.longsheng.tools.common.utils.ServletUtils;
import com.longsheng.tools.modules.appEntry.entity.WeiUser;
import com.longsheng.tools.modules.customer.entity.FrontUser;
import com.longsheng.tools.system.service.SysTenantService;
import com.longsheng.tools.system.service.SysUserInfoService;
import com.longsheng.tools.system.vo.UserInfoVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.AntPathMatcher;
import org.springframework.web.filter.OncePerRequestFilter;

import javax.servlet.FilterChain;
import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.Map;
import java.util.Objects;

/**
 * 多租户 Security Web 过滤器
 * 1. 如果是登陆的用户，校验是否有权限访问该租户，避免越权问题。
 * 2. 如果请求未带租户的编号，检查是否是忽略的 URL，否则也不允许访问。
 * 3. 校验租户是合法，例如说被禁用、到期
 */
@Slf4j
public class TenantSecurityWebFilter extends OncePerRequestFilter {

    private final TenantProperties tenantProperties;

    private final Map<String, IUser> userInfoService;

    private final AntPathMatcher pathMatcher;

    private final GlobalExceptionHandler globalExceptionHandler;
    private final SysTenantService tenantService;
    private final SaTokenStpInterfaceImpl stpInterface;

    public TenantSecurityWebFilter(TenantProperties tenantProperties,
                                   Map<String, IUser> userInfoService,
                                   GlobalExceptionHandler globalExceptionHandler,
                                   SysTenantService tenantService, SaTokenStpInterfaceImpl stpInterface) {
        this.tenantProperties = tenantProperties;
        this.pathMatcher = new AntPathMatcher();
        this.userInfoService = userInfoService;
        this.globalExceptionHandler = globalExceptionHandler;
        this.tenantService = tenantService;
        this.stpInterface = stpInterface;
    }

    @Override
    protected void doFilterInternal(HttpServletRequest request, HttpServletResponse response, FilterChain chain)
            throws ServletException, IOException {
        Long tenantId = TenantContextHolder.getTenantId();
        // 1. 登陆的用户，校验是否有权限访问该租户，避免越权问题。
        TenantBaseDO<?> tenantBaseDO = null;
        Object currentUser = null;
        for (IUser userService : userInfoService.values()) {
            try {
                currentUser = userService.getCurrentUser();
                if (currentUser != null) {
                    if (currentUser instanceof TenantBaseDO) {
                        tenantBaseDO = (TenantBaseDO<?>) currentUser;
                    }
                    break;
                }
            } catch (Exception e) {

            }
        }
        if (currentUser != null && tenantBaseDO != null) {
            // 如果获取不到租户编号，则尝试使用登陆用户的租户编号
            if (tenantId == null) {
                tenantId = tenantBaseDO.getTenantId();
                TenantContextHolder.setTenantId(tenantId);
                // 如果传递了租户编号，则进行比对租户编号，避免越权问题
            } else if (!Objects.equals(tenantBaseDO.getTenantId(), TenantContextHolder.getTenantId())) {
                log.error("[doFilterInternal][租户({}) User({}) 越权访问租户({}) URL({}/{})]",
                        tenantBaseDO.getTenantId(), JSON.toJSONString(currentUser),
                        TenantContextHolder.getTenantId(), request.getRequestURI(), request.getMethod());
                ServletUtils.writeJSON(response, RES.no(403,
                        "您无权访问该租户的数据"));
                return;
            }
        }

        // 如果非允许忽略租户的 URL，则校验租户是否合法
        if (!isIgnoreUrl(request)) {
            // 2. 如果请求未带租户的编号，不允许访问。
            if (tenantId == null) {
                UserInfoVo user = null;
                boolean isBack = false;
                if (currentUser instanceof UserInfoVo) {
                    isBack = true;
                    user = (UserInfoVo) currentUser;
                }
                if (isBack && UserTypeEnum.PLAT.getCode().equals(user.getUserType())) {
                    TenantContextHolder.setIgnore(true);
                } else {
                    log.error("[doFilterInternal][URL({}/{}) 未传递租户编号]", request.getRequestURI(), request.getMethod());
                    ServletUtils.writeJSON(response, RES.no(StatusEnums.BAD_REQUEST.getCode(),
                            "请求的租户标识未传递，请进行排查"));
                    return;
                }
            }
            // 3. 校验租户是合法，例如说被禁用、到期
            try {
                tenantService.validTenant(tenantId);
            } catch (Exception ex) {
                RES result = globalExceptionHandler.allExceptionHandler(request, ex);
                ServletUtils.writeJSON(response, result);
                return;
            }
        } else { // 如果是允许忽略租户的 URL，若未传递租户编号，则默认忽略租户编号，避免报错
            if (tenantId == null) {
                TenantContextHolder.setIgnore(true);
            }
        }
        if (currentUser instanceof UserInfoVo) {
            TenantContextHolder.setUserType(UserTypeEnum.getByCode(((UserInfoVo) currentUser).getUserType()));
            TenantContextHolder.setUserRoles(stpInterface.getRoleList(((UserInfoVo) currentUser).getId(), ""));
        }

        // 继续过滤
        chain.doFilter(request, response);
    }

    private boolean isIgnoreUrl(HttpServletRequest request) {
        // 快速匹配，保证性能
        if (CollUtil.contains(tenantProperties.getIgnoreUrls(), request.getRequestURI())) {
            return true;
        }
        // 逐个 Ant 路径匹配
        for (String url : tenantProperties.getIgnoreUrls()) {
            if (pathMatcher.match(url, request.getRequestURI())) {
                return true;
            }
        }
        return false;
    }

}
