package com.longsheng.tools.common.gm;

import lombok.Data;

import java.math.BigDecimal;


@Data
public class GmPaymentParam {

    /**
     * 客户请求标识,最长32位
     * (用户提现时商户系统自动生成，每个requestId代表一个提现请求，工猫作为每笔提现记录的唯一标识)
     */
    private String requestId;

    /**
     * 手机号码
     */
    private String mobile;

    /**
     * 姓名
     */
    private String name;

    /**
     * 提现金额 (注意：支付宝实发金额最低0.1元 微信实发金额最低0.3元)
     * 单位：元
     */
    private BigDecimal amount;

    /**
     * 证件号码
     */
    private String identity;

    /**
     * 入款账号 银行卡提现此字段传入员工绑定的银行卡，
     * 支付宝提现此字段请传入员工绑定的支付宝账号，当是微信提现时这个字段对应的是openId
     */
    private String bankAccount;

    /**
     * 申请时间(yyyyMMddHHmmss)
     */
    private String dateTime;

    /**
     * 单据描叙，最长20位（非必填）
     */
    private String remark;

    /**
     * 扩展备注，最长32位，此字段不会展示给用户（非必填）
     */
    private String extRemark;

    /**
     * 商户提现账户类型
     * 银行通道:BANK, 微信通道:WECHAT, 支付宝通道:ALIPAY 例如：传BANK会从商户银行账户扣款
     */
    private String salaryType;

    /**
     * 支付宝收款账户类型
     * （当salaryType是支付宝通道(ALIPAY)时，用来区分支付宝收款账户类型：
     * LOGON_ID(支付宝账号)，USER_ID(用户标识)，BANKCARD_ACCOUNT(银行卡)，
     * 不传默认支付宝账号）
     * （非必填）
     */
    private String alipayAccountType;

    /**
     * 微信APPID
     * (当提现类型为微信渠道时必填，同时须保证openid属于当前传递的wxAppid，且跟工猫微信商户号绑定过)。
     * （非必填）
     */
    private String wxAppid;
}