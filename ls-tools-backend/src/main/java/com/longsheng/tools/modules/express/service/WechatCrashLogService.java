package com.longsheng.tools.modules.express.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.longsheng.tools.common.utils.RES;
import com.longsheng.tools.common.utils.TableInfo;
import com.longsheng.tools.modules.express.entity.WechatCrashLog;

import java.util.List;

/**
 * 微信崩溃日志Service接口
 *
 * <AUTHOR>
 * @date 2024-12-28
 */
public interface WechatCrashLogService extends IService<WechatCrashLog> {

    /**
     * 查询微信崩溃日志列表
     *
     * @param wechatCrashLog 微信崩溃日志
     * @param isExport 是否导出，0-否，1-是
     * @return 微信崩溃日志集合
     */
    TableInfo list(WechatCrashLog wechatCrashLog, int isExport);

    /**
     * 获取微信崩溃日志详细信息
     *
     * @param id 主键
     * @return 微信崩溃日志
     */
    RES getWechatCrashLog(Integer id);

    /**
     * 新增微信崩溃日志
     *
     * @param wechatCrashLog 微信崩溃日志
     * @return 结果
     */
    RES add(WechatCrashLog wechatCrashLog);

    /**
     * 修改微信崩溃日志
     *
     * @param wechatCrashLog 微信崩溃日志
     * @return 结果
     */
    RES update(WechatCrashLog wechatCrashLog);

    /**
     * 批量删除微信崩溃日志
     *
     * @param ids 需要删除的微信崩溃日志主键集合
     * @return 结果
     */
    RES delete(Integer[] ids);

    /**
     * 根据IP地址查询崩溃日志
     *
     * @param ip IP地址
     * @return 崩溃日志列表
     */
    List<WechatCrashLog> getByIp(String ip);

    /**
     * 根据错误信息关键字查询崩溃日志
     *
     * @param keyword 关键字
     * @return 崩溃日志列表
     */
    List<WechatCrashLog> getByErrorKeyword(String keyword);
}
