package com.longsheng.tools.modules.wxApp.park.utils;

import cn.hutool.core.lang.Assert;
import com.longsheng.tools.common.exception.CustomException;
import com.longsheng.tools.modules.wxApp.park.config.ParkSmsConfig;
import com.longsheng.tools.system.service.SysDictDetailService;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

@Component
public class ParkSmsClient {
    @Resource
    private SysDictDetailService sysDictDetailService;

    public String getContent( String plateNumber) {
        ParkSmsConfig parkSmsConfig = sysDictDetailService.getSysDictDetailByDictCode(ParkSmsConfig.MAP_DICT_TYPE_NAME, ParkSmsConfig.class);
        Assert.notNull(parkSmsConfig.getTemplate(), () -> new CustomException("没有配置模板"));
        return String.format(parkSmsConfig.getTemplate(), plateNumber);
    }
}
