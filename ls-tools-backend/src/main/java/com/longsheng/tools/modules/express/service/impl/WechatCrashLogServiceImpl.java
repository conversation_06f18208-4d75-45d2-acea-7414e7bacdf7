package com.longsheng.tools.modules.express.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.longsheng.tools.common.utils.PageUtils;
import com.longsheng.tools.common.utils.RES;
import com.longsheng.tools.common.utils.TableInfo;
import com.longsheng.tools.modules.express.entity.WechatCrashLog;
import com.longsheng.tools.modules.express.mapper.WechatCrashLogMapper;
import com.longsheng.tools.modules.express.service.WechatCrashLogService;
import com.longsheng.tools.system.vo.PageVO;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.Date;
import java.util.List;

/**
 * 微信崩溃日志Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-12-28
 */
@Service
public class WechatCrashLogServiceImpl extends ServiceImpl<WechatCrashLogMapper, WechatCrashLog> implements WechatCrashLogService {

    @Override
    public TableInfo list(WechatCrashLog wechatCrashLog, int isExport) {
        // 获取分页对象
        PageVO pageVO = PageUtils.getPageVO();
        if (1 == isExport) {
            pageVO.setPageSize(Integer.MAX_VALUE);
        }
        
        // 查询条件
        QueryWrapper<WechatCrashLog> queryWrapper = new QueryWrapper<>();
        
        // 根据IP地址查询
        if (StringUtils.isNotBlank(wechatCrashLog.getIp())) {
            queryWrapper.like("ip", wechatCrashLog.getIp());
        }
        
        // 根据错误信息查询
        if (StringUtils.isNotBlank(wechatCrashLog.getErrorMsg())) {
            queryWrapper.like("error_msg", wechatCrashLog.getErrorMsg());
        }
        
        // 根据创建时间范围查询
        if (StringUtils.isNotBlank(pageVO.getBeginTime())) {
            queryWrapper.ge("create_date", pageVO.getBeginTime());
        }
        if (StringUtils.isNotBlank(pageVO.getEndTime())) {
            queryWrapper.le("create_date", pageVO.getEndTime());
        }
        
        // 排序规则
        if (StringUtils.isNotBlank(pageVO.getField())) {
            // 正序
            if (pageVO.getIsAsc()) {
                queryWrapper.orderByAsc(pageVO.getField());
            }
            // 倒序
            else {
                queryWrapper.orderByDesc(pageVO.getField());
            }
        }
        // 默认排序规则
        else {
            queryWrapper.lambda().orderByDesc(WechatCrashLog::getCreateDate);
        }
        
        return TableInfo.ok(this.baseMapper.selectPage(new Page<>(pageVO.getPageNum(), pageVO.getPageSize()), queryWrapper));
    }

    @Override
    public RES getWechatCrashLog(Integer id) {
        if (null == id) {
            return RES.no("ID不能为空");
        }
        return RES.ok(this.baseMapper.selectById(id));
    }

    @Override
    public RES add(WechatCrashLog wechatCrashLog) {
        if (null == wechatCrashLog) {
            return RES.no("数据错误");
        }
        
        // 设置创建时间
        if (null == wechatCrashLog.getCreateDate()) {
            wechatCrashLog.setCreateDate(new Date());
        }
        
        this.baseMapper.insert(wechatCrashLog);
        return RES.ok();
    }

    @Override
    public RES update(WechatCrashLog wechatCrashLog) {
        if (null == wechatCrashLog || null == wechatCrashLog.getId() || 0 == wechatCrashLog.getId()) {
            return RES.no("数据错误");
        }
        
        this.baseMapper.updateById(wechatCrashLog);
        return RES.ok();
    }

    @Override
    public RES delete(Integer[] ids) {
        if (null == ids || 0 == ids.length) {
            return RES.no("请选择需要操作的数据");
        }
        
        LambdaQueryWrapper<WechatCrashLog> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(WechatCrashLog::getId, Arrays.asList(ids));
        this.baseMapper.delete(queryWrapper);
        return RES.ok();
    }

    @Override
    public List<WechatCrashLog> getByIp(String ip) {
        if (StringUtils.isBlank(ip)) {
            return Arrays.asList();
        }
        
        LambdaQueryWrapper<WechatCrashLog> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(WechatCrashLog::getIp, ip);
        queryWrapper.orderByDesc(WechatCrashLog::getCreateDate);
        return this.baseMapper.selectList(queryWrapper);
    }

    @Override
    public List<WechatCrashLog> getByErrorKeyword(String keyword) {
        if (StringUtils.isBlank(keyword)) {
            return Arrays.asList();
        }
        
        LambdaQueryWrapper<WechatCrashLog> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.like(WechatCrashLog::getErrorMsg, keyword);
        queryWrapper.orderByDesc(WechatCrashLog::getCreateDate);
        return this.baseMapper.selectList(queryWrapper);
    }
}
