package com.longsheng.tools.modules.express.service;

import com.longsheng.tools.modules.express.entity.ExpUserPrice;
import com.longsheng.tools.modules.express.params.BatchBindPriceCommand;
import com.longsheng.tools.modules.express.params.BindMsgTmpCommand;
import com.longsheng.tools.modules.express.vo.PriceDto;
import com.longsheng.tools.modules.express.vo.UserPriceVO;
import com.longsheng.tools.system.vo.TableInfo;
import com.longsheng.tools.common.utils.RES;
import com.baomidou.mybatisplus.extension.service.IService;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * Service接口
 *
 * <AUTHOR>
 * @date 2024-12-28
 */
public interface ExpUserPriceService extends IService<ExpUserPrice> {

    /**
    * 查询ExpUserPrice
    *
    * @param isExport 是否导出，0-否，1-是
    * @return
    */
    TableInfo list(ExpUserPrice expUserPrice, int isExport);

    /**
     * 获取ExpUserPrice
     */
    RES getExpUserPrice(Long id);

    /**
     * 新增ExpUserPrice
     */
    RES add(ExpUserPrice expUserPrice);

    /**
     * 修改ExpUserPrice
     */
    RES update(ExpUserPrice expUserPrice);

    /**
     * 删除ExpUserPrice
     */
    RES delete(Long[] ids);

    void removeByUserAndPrice(Long userId, Long priceId);

    void saveRef(ExpUserPrice userPrice);

    List<ExpUserPrice> getUserPriceList(Long userId);

    @Transactional
    Boolean batchBind(BatchBindPriceCommand command);

    List<UserPriceVO> getUserPrice(Long userId);

    Integer queryDiscountByUser(Long userId, Long priceId);

    Boolean bindMsgTmp(BindMsgTmpCommand command);

    String convertMsg(String tmp, PriceDto dto);
}
