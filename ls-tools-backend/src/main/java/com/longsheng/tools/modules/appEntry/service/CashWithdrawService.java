package com.longsheng.tools.modules.appEntry.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.longsheng.tools.common.gm.GmPayNotifyParam;
import com.longsheng.tools.common.utils.RES;
import com.longsheng.tools.modules.appEntry.entity.CashWithdraw;
import com.longsheng.tools.modules.appEntry.enums.WxTaskAuditEnum;
import com.longsheng.tools.modules.appEntry.params.GetAdminCashWithPageParams;
import com.longsheng.tools.modules.appEntry.params.WeiMoneyAddParams;
import com.longsheng.tools.modules.appEntry.vo.AdminCashWithPageVo;
import com.longsheng.tools.system.vo.TableInfo;

import java.util.List;

/**
 * Service接口
 *
 * <AUTHOR>
 * @date 2024-09-13
 */
public interface CashWithdrawService extends IService<CashWithdraw> {

    /**
    * 查询CashWithdraw
    *
    * @param isExport 是否导出，0-否，1-是
    * @return
    */
    TableInfo list(CashWithdraw cashWithdraw, int isExport);

    /**
     * 获取CashWithdraw
     */
    RES getCashWithdraw(Long id);

    /**
     * 新增CashWithdraw
     */
    RES add(CashWithdraw cashWithdraw);

    /**
     * 修改CashWithdraw
     */
    RES update(CashWithdraw cashWithdraw);

    /**
     * 删除CashWithdraw
     */
    RES delete(Long[] ids);

    void confirmWithdraw(Long id);

    List<CashWithdraw> queryWithdrawByStatus(WxTaskAuditEnum wxTaskAuditEnum);

    Page<AdminCashWithPageVo> getAdminCashWithPage(GetAdminCashWithPageParams getAdminCashWithPageParams);

    void auditCashWithdraw(Long id);

    void applyWithdraw(WeiMoneyAddParams param);

    void rejectWithdraw(Long id, String msg);

    void cashWithdrawRefund(CashWithdraw withdraw);

    void gmNotify(GmPayNotifyParam param);
}
