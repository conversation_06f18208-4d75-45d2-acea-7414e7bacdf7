package com.longsheng.tools.modules.express.controller;

import com.longsheng.tools.common.utils.RES;
import com.longsheng.tools.common.utils.TableInfo;
import com.longsheng.tools.modules.express.entity.WechatCrashLog;
import com.longsheng.tools.modules.express.service.WechatCrashLogService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * 微信崩溃日志Controller
 *
 * <AUTHOR>
 * @date 2024-12-28
 */
@RestController
@RequestMapping("/express/wechat-crash-log")
public class WechatCrashLogController {

    @Autowired
    private WechatCrashLogService wechatCrashLogService;

    /**
     * 查询微信崩溃日志列表
     */
    @GetMapping("/list")
    public TableInfo list(WechatCrashLog wechatCrashLog) {
        return wechatCrashLogService.list(wechatCrashLog, 0);
    }

    /**
     * 获取微信崩溃日志详细信息
     */
    @GetMapping(value = "/{id}")
    public RES getWechatCrashLog(@PathVariable("id") Integer id) {
        return wechatCrashLogService.getWechatCrashLog(id);
    }

    /**
     * 新增微信崩溃日志
     */
    @PostMapping
    public RES add(@RequestBody WechatCrashLog wechatCrashLog) {
        return wechatCrashLogService.add(wechatCrashLog);
    }

    /**
     * 修改微信崩溃日志
     */
    @PutMapping
    public RES edit(@RequestBody WechatCrashLog wechatCrashLog) {
        return wechatCrashLogService.update(wechatCrashLog);
    }

    /**
     * 删除微信崩溃日志
     */
    @DeleteMapping("/{ids}")
    public RES remove(@PathVariable Integer[] ids) {
        return wechatCrashLogService.delete(ids);
    }

    /**
     * 根据IP地址查询崩溃日志
     */
    @GetMapping("/by-ip/{ip}")
    public RES getByIp(@PathVariable String ip) {
        return RES.ok(wechatCrashLogService.getByIp(ip));
    }

    /**
     * 根据错误关键字查询崩溃日志
     */
    @GetMapping("/by-error/{keyword}")
    public RES getByErrorKeyword(@PathVariable String keyword) {
        return RES.ok(wechatCrashLogService.getByErrorKeyword(keyword));
    }
}
