package com.longsheng.tools.modules.commonExpre.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.longsheng.tools.common.exception.CustomException;
import com.longsheng.tools.common.utils.PageUtils;
import com.longsheng.tools.modules.commonExpre.entity.UserCommonExpressions;
import com.longsheng.tools.modules.commonExpre.event.RedisToMysqlUserCount;
import com.longsheng.tools.modules.commonExpre.mapper.UserCommonExpressionsMapper;
import com.longsheng.tools.modules.commonExpre.params.GetCommonParam;
import com.longsheng.tools.modules.commonExpre.params.IncrUserCommonParam;
import com.longsheng.tools.modules.commonExpre.params.InsertUserCommonParam;
import com.longsheng.tools.modules.commonExpre.params.UserCommonByUserIdParams;
import com.longsheng.tools.modules.commonExpre.service.UserCommonExpressionsService;
import com.longsheng.tools.modules.commonExpre.vo.CeTypeOptionalsVo;
import com.longsheng.tools.modules.commonExpre.vo.UserCommonByUserIdVo;
import com.longsheng.tools.modules.commonExpre.vo.UserCommonVo;
import com.longsheng.tools.modules.order.entity.Order;
import com.longsheng.tools.modules.order.vo.OrderByUserIdVo;
import com.longsheng.tools.modules.tools.service.AutoToolsService;
import com.longsheng.tools.modules.tools.service.impl.AutoToolsServiceImpl;
import com.longsheng.tools.modules.tools.vo.ToolOptionalsVo;
import com.longsheng.tools.system.vo.PageVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.context.ApplicationContext;
import org.springframework.core.task.TaskExecutor;
import org.springframework.data.redis.core.DefaultTypedTuple;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.ZSetOperations;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Service
@Slf4j
public class UserCommonExpressionsServiceImplV1 implements UserCommonExpressionsService {
    @Resource
    private UserCommonExpressionsMapper userCommonExpressionsMapper;

    @Resource
    private RedisTemplate redisTemplate;

    @Resource
    TaskExecutor taskExecutor;

    @Resource
    AutoToolsService autoToolsService;

    @Resource
    private ApplicationContext applicationContext;
    //   过期时间 单位是天
    public static final long EXPIRE_TIME = 3;
    //   短的过期时间 单位是分钟
    public static final int SHORT_EXPIRE_TIME = 20;


    public static final String USER_COMMON_KEY_PREFIX = "userCommon:";
    //    #分隔符
    private static final String SPLIT_CHAR = "#";
    private static final String DEFAULT_SPLIT = "@";

    private String getUserCommonKey(Long toolId, String ceType) {
        return USER_COMMON_KEY_PREFIX + toolId + ":" + ceType;
    }


    private List<UserCommonVo> getCommonByParam(Long toolId, String ceType) {
//        去查数据库
        List<UserCommonExpressions> userCommonExpressions = userCommonExpressionsMapper.selectList(new LambdaQueryWrapper<UserCommonExpressions>()
                .eq(UserCommonExpressions::getToolId, toolId)
                .eq(UserCommonExpressions::getCeType, ceType)
                .orderByDesc(UserCommonExpressions::getUseCount));
        return userCommonExpressions.stream().map(userCommonExpressions1 -> {
            UserCommonVo userCommonVo = new UserCommonVo();
            userCommonVo.setId(userCommonExpressions1.getId());
            userCommonVo.setContent(userCommonExpressions1.getContent());
            userCommonVo.setUseCount(userCommonExpressions1.getUseCount());
            return userCommonVo;
        }).collect(Collectors.toList());
    }

    @Override
    public Page<UserCommonByUserIdVo> getUserCommonByUserId(UserCommonByUserIdParams userCommonByUserIdParams) {

        LambdaQueryWrapper<UserCommonExpressions> userCommonExpressionsQueryWrapper = new LambdaQueryWrapper<>();
        userCommonExpressionsQueryWrapper.eq(UserCommonExpressions::getUserId, userCommonByUserIdParams.getUserId());
        userCommonExpressionsQueryWrapper.eq(!ObjectUtils.isEmpty(userCommonByUserIdParams.getToolId()), UserCommonExpressions::getToolId, userCommonByUserIdParams.getToolId());
        userCommonExpressionsQueryWrapper.like(StringUtils.hasText(userCommonByUserIdParams.getContent()), UserCommonExpressions::getContent, userCommonByUserIdParams.getContent());
        userCommonExpressionsQueryWrapper.eq(StringUtils.hasText(userCommonByUserIdParams.getCeType()), UserCommonExpressions::getCeType, userCommonByUserIdParams.getCeType());
        userCommonExpressionsQueryWrapper.ge(!ObjectUtils.isEmpty(userCommonByUserIdParams.getCreateTime()), UserCommonExpressions::getCreateTime, userCommonByUserIdParams.getCreateTime());
        userCommonExpressionsQueryWrapper.eq(!ObjectUtils.isEmpty(userCommonByUserIdParams.getIsDefault()), UserCommonExpressions::getIsDefault, userCommonByUserIdParams.getIsDefault());
        PageVO pageVO = PageUtils.getPageVO();
        userCommonExpressionsQueryWrapper.orderByDesc(UserCommonExpressions::getCreateTime);
        Page<UserCommonExpressions> objectPage = new Page<>(pageVO.getPageNum(), pageVO.getPageSize());
        Page<UserCommonExpressions> userCommonExpressionsPage = userCommonExpressionsMapper.selectPage(objectPage, userCommonExpressionsQueryWrapper);
//      进行转化
        Page<UserCommonByUserIdVo> userCommonByUserIdVoPage = new Page<>();
        userCommonByUserIdVoPage.setTotal(userCommonExpressionsPage.getTotal());


        List<ToolOptionalsVo> allEnableTool = autoToolsService.getAllEnableTool();
//        转为map
        Map<Long, String> toolMap = allEnableTool.stream().collect(Collectors.toMap(ToolOptionalsVo::getValue, ToolOptionalsVo::getLabel));

        List<UserCommonByUserIdVo> collect = userCommonExpressionsPage.getRecords().stream().map(
                userCommonExpressions -> {
                    UserCommonByUserIdVo userCommonByUserIdVo = new UserCommonByUserIdVo();
                    userCommonByUserIdVo.setContent(userCommonExpressions.getContent());
                    userCommonByUserIdVo.setCreateTime(userCommonExpressions.getCreateTime());
                    userCommonByUserIdVo.setUpdateTime(userCommonExpressions.getUpdateTime());
                    userCommonByUserIdVo.setUseCount(userCommonExpressions.getUseCount());
                    userCommonByUserIdVo.setCeType(userCommonExpressions.getCeType());
                    userCommonByUserIdVo.setIsDefault(userCommonExpressions.getIsDefault());
                    userCommonByUserIdVo.setToolName(toolMap.get(userCommonExpressions.getToolId()));
                    return userCommonByUserIdVo;
                }
        ).collect(Collectors.toList());
        userCommonByUserIdVoPage.setRecords(collect);

        return userCommonByUserIdVoPage;
    }

    @Override
    public List<CeTypeOptionalsVo> getUserCeTypeByUserId(Long userId) {

        LambdaQueryWrapper<UserCommonExpressions> userCommonExpressionsLambdaQueryWrapper = new LambdaQueryWrapper<>();
        userCommonExpressionsLambdaQueryWrapper.eq(UserCommonExpressions::getUserId, userId);
        userCommonExpressionsLambdaQueryWrapper.select(UserCommonExpressions::getCeType);
        List<UserCommonExpressions> userCommonExpressions = userCommonExpressionsMapper.selectList(userCommonExpressionsLambdaQueryWrapper);

        if (!CollectionUtils.isEmpty(userCommonExpressions)) {
            return userCommonExpressions.stream().map(userCommonExpressions1 -> {
                CeTypeOptionalsVo ceTypeOptionalsVo = new CeTypeOptionalsVo();
                ceTypeOptionalsVo.setValue(userCommonExpressions1.getCeType());
                ceTypeOptionalsVo.setLabel(userCommonExpressions1.getCeType());
                return ceTypeOptionalsVo;
            }).distinct().collect(Collectors.toList());
        }
        return Collections.emptyList();
    }

    @Override
    public List<UserCommonVo> getCommon(GetCommonParam getCommonParam) {

        List<UserCommonVo> result = null;

        String userCommonKey = getUserCommonKey(getCommonParam.getToolId(), getCommonParam.getCeType());

        Set<DefaultTypedTuple<String>> userCommons = redisTemplate.opsForZSet().reverseRangeWithScores(userCommonKey, 0, -1);
        Set<String> range = redisTemplate.opsForZSet().range(userCommonKey, 0, -1);
        if (!CollectionUtils.isEmpty(userCommons)) {
//            if (range.size() == 1 && range.contains(SPLIT_CHAR)) {
//                redisTemplate.expire(userCommonKey, SHORT_EXPIRE_TIME, TimeUnit.MINUTES);
//                return result;
//            }
            log.info("redis缓存命中");
            redisTemplate.expire(userCommonKey, EXPIRE_TIME, TimeUnit.DAYS);
            return redisStringToUserCommonVo(userCommons);
        }

        result = getCommonByParam(getCommonParam.getToolId(), getCommonParam.getCeType());
        if (CollectionUtils.isEmpty(result)) {
//            redisTemplate.opsForZSet().add(userCommonKey, SPLIT_CHAR, 0);
//            redisTemplate.expire(userCommonKey, SHORT_EXPIRE_TIME, TimeUnit.MINUTES);
            return result;
        }
        Set<ZSetOperations.TypedTuple<Object>> typedTuples = userCommonVoZset(result);
        redisTemplate.opsForZSet().add(userCommonKey, typedTuples);
        redisTemplate.expire(userCommonKey, EXPIRE_TIME, TimeUnit.DAYS);
        return result;
    }

    private List<UserCommonVo> redisStringToUserCommonVo(Set<DefaultTypedTuple<String>> userCommons) {
        return userCommons.stream().map(s -> {
            UserCommonVo userCommonVo = new UserCommonVo();
            String[] split = s.getValue().split(SPLIT_CHAR);
            userCommonVo.setId(Long.valueOf(split[0]));
            userCommonVo.setContent(split[1]);
            userCommonVo.setUseCount(s.getScore().intValue());
            if (split.length == 3 && DEFAULT_SPLIT.equals(split[2])) {
                userCommonVo.setIsDefault(true);
            }
            return userCommonVo;
        }).collect(Collectors.toList());
    }

    private Set<ZSetOperations.TypedTuple<Object>> userCommonVoZset(List<UserCommonVo> userCommonVos) {
        return userCommonVos.stream()
                .map(userCommonVo ->
                        new DefaultTypedTuple<Object>(userCommonVo.getId() + SPLIT_CHAR + userCommonVo.getContent(),
                                (double) userCommonVo.getUseCount()))
                .collect(Collectors.toSet());
    }

    @Override
    public void saveUserCommon(InsertUserCommonParam insertUserCommonParam) {
        String userCommonKey = getUserCommonKey(insertUserCommonParam.getToolId(), insertUserCommonParam.getCeType());

        taskExecutor.execute(() -> {
            try {
                UserCommonExpressions userCommonExpressions = new UserCommonExpressions();
                BeanUtils.copyProperties(insertUserCommonParam, userCommonExpressions);
//                保存到数据库
                userCommonExpressionsMapper.insert(userCommonExpressions);

//                删除临时数据
//                Set<String> range = redisTemplate.opsForZSet().range(userCommonKey, 0, -1);
//                if (!CollectionUtils.isEmpty(range)) {
//                    if (range.size() == 1 && range.contains(SPLIT_CHAR)) {
//                        redisTemplate.delete(userCommonKey);
//                    }
//                }


                redisTemplate.opsForZSet().add(userCommonKey, insertUserCommonParam.getContent() + SPLIT_CHAR + userCommonExpressions.getId(), 1);
                redisTemplate.expire(userCommonKey, EXPIRE_TIME, TimeUnit.DAYS);
            } catch (Exception e) {
                log.warn("{}用户，保存常用语失败，类型是：{}，内容是：{}",
                        insertUserCommonParam.getUserId(),
                        insertUserCommonParam.getCeType(),
                        insertUserCommonParam.getContent());
            }
        });

    }

    @Override
    public void incrUserCommon(IncrUserCommonParam incrUserCommonParam) {
        String userCommonKey = getUserCommonKey(incrUserCommonParam.getToolId(), incrUserCommonParam.getCeType());
        if (!redisTemplate.hasKey(userCommonKey)) {
//            根据id判断在不在
            Long l = userCommonExpressionsMapper.incrUserCommonById(1, incrUserCommonParam.getId());
            if (l == 1) {
                List<UserCommonVo> commonByParam = getCommonByParam(incrUserCommonParam.getToolId(), incrUserCommonParam.getCeType());
                Set<ZSetOperations.TypedTuple<Object>> typedTuples = userCommonVoZset(commonByParam);
                redisTemplate.opsForZSet().add(userCommonKey, typedTuples);
                redisTemplate.expire(userCommonKey, EXPIRE_TIME, TimeUnit.DAYS);
                return;
            }
            throw new CustomException("常用语不存在");
        }
        redisTemplate.opsForZSet().incrementScore(userCommonKey,
                incrUserCommonParam.getId() + SPLIT_CHAR + incrUserCommonParam.getContent(),
                1);
//      发布事件 进行异步更新
        applicationContext.publishEvent(new RedisToMysqlUserCount(incrUserCommonParam.getId()));
    }
}
