package com.longsheng.tools.modules.order.service.impl;

import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.binarywang.wxpay.exception.WxPayException;
import com.longsheng.tools.common.consts.CommonConstants;
import com.longsheng.tools.common.enums.PayOrderStatusRespEnum;
import com.longsheng.tools.common.payment.AliPayService;
import com.longsheng.tools.common.payment.WechatPayService;
import com.longsheng.tools.common.payment.dto.PayOrderRespDTO;
import com.longsheng.tools.common.payment.dto.WechatPayDTO;
import com.longsheng.tools.common.satoken.StpUserUtil;
import com.longsheng.tools.common.utils.DateUtils;
import com.longsheng.tools.common.utils.PageUtils;
import com.longsheng.tools.modules.customer.service.FrontUserService;
import com.longsheng.tools.modules.order.entity.Order;
import com.longsheng.tools.modules.order.entity.PaymentRecord;
import com.longsheng.tools.modules.order.enums.PaymentStatusEnum;
import com.longsheng.tools.modules.order.mapper.PaymentRecordMapper;
import com.longsheng.tools.modules.order.params.PayRecordListByUserIdParams;
import com.longsheng.tools.modules.order.service.OrderService;
import com.longsheng.tools.modules.order.service.PaymentRecordService;
import com.longsheng.tools.modules.order.vo.PayRecordListByUserIdVo;
import com.longsheng.tools.system.vo.PageVO;
import com.longsheng.tools.system.vo.TableInfo;
import com.longsheng.tools.system.service.ArticlePaymentService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

/**
 * Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-05-14
 */
@Slf4j
@Service
public class PaymentRecordServiceImpl extends ServiceImpl<PaymentRecordMapper, PaymentRecord> implements PaymentRecordService {

    @Resource
    private OrderService orderService;

    @Resource
    private AliPayService aliPayService;

    @Resource
    private WechatPayService wechatPayService;

    @Resource
    private FrontUserService userService;

    @Resource
    private ArticlePaymentService articlePaymentService;

    @Override
    public TableInfo list(PaymentRecord record, int isExport) {
        // 获取分页对象
        PageVO pageVO = PageUtils.getPageVO();
        if (1 == isExport) {
            pageVO.setPageSize(Integer.MAX_VALUE);
        }
        // 查询条件
        QueryWrapper<PaymentRecord> queryWrapper = new QueryWrapper<>();
        /**
         * TODO 自己实现查询条件
         */
        // 时间段查询
        if (StringUtils.isNotBlank(pageVO.getBeginTime())) {
            queryWrapper.lambda().ge(PaymentRecord::getCreateTime, DateUtils.completionTimeStart(pageVO.getBeginTime()));
        }
        if (StringUtils.isNotBlank(pageVO.getEndTime())) {
            queryWrapper.lambda().le(PaymentRecord::getCreateTime, DateUtils.completionTimeEnd(pageVO.getEndTime()));
        }
        // 排序规则
        if (StringUtils.isNotBlank(pageVO.getField())) {
            // 正序
            if (pageVO.getIsAsc()) {
                queryWrapper.orderByAsc(pageVO.getField());
            }
            // 倒序
            else {
                queryWrapper.orderByDesc(pageVO.getField());
            }
        }
        // 默认排序规则
        else {
            queryWrapper.lambda().orderByDesc(PaymentRecord::getCreateTime);
        }
        return TableInfo.ok(this.baseMapper.selectPage(new Page<>(pageVO.getPageNum(), pageVO.getPageSize()), queryWrapper));
    }

    @Override
    public PaymentRecord getPaymentRecord(Long recordId) {
        return this.baseMapper.selectById(recordId);
    }

    @Override
    public String aliPay(Long orderId) {
        Order order = orderService.getOrder(orderId);
        PaymentRecord record = queryPendingByOrderId(orderId);
        if (record == null) {
            record = createPaymentRecord(orderId, order.getDiscountPrice(), CommonConstants.ALI_PAY);
        }
        if (!CommonConstants.ALI_PAY.equals(record.getPayChannel())) {
            changePaymentChannel(record, CommonConstants.ALI_PAY);
        }
        return aliPayService.alipay(String.valueOf(record.getRecordId()));
    }


    @Override
    public WechatPayDTO wechatPay(Long orderId) {
        log.info("开始微信支付，订单id:{}", orderId);
        Order order = orderService.getOrder(orderId);
        PaymentRecord record = queryPendingByOrderId(orderId);
        if (record == null) {
            record = createPaymentRecord(orderId, order.getDiscountPrice(), CommonConstants.WX_PAY);
        }
        if (!CommonConstants.WX_PAY.equals(record.getPayChannel())) {
            changePaymentChannel(record, CommonConstants.WX_PAY);
        }
        return wechatPayService.wechatPay(String.valueOf(record.getRecordId()), order.getDiscountPrice(), order.getProductName());
    }

    private PaymentRecord createPaymentRecord(Long orderId, BigDecimal price, String paymentType) {
        PaymentRecord record = new PaymentRecord();
        record.setUserId(StpUserUtil.getLoginIdAsLong());
        record.setOrderId(orderId);
        record.setTotalAmount(price);
        record.setPaymentStatus(PaymentStatusEnum.PENDING);
        record.setPayChannel(paymentType);
        this.save(record);
        return record;
    }

    private void changePaymentChannel(PaymentRecord record, String paymentType) {
        record.setPayChannel(paymentType);
        this.updateById(record);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void wechatPayCallback(String body) {
        try {
            PayOrderRespDTO payOrderRespDTO = wechatPayService.doParseOrderNotify(body);
            log.info("解析后微信支付结果：{}", JSON.toJSONString(payOrderRespDTO));
            String outTradeNo = payOrderRespDTO.getOutTradeNo();

            // Attempt to process as a regular order payment
            try {
                long recordId = Long.parseLong(outTradeNo);
                PaymentRecord paymentRecord = this.getPaymentRecord(recordId);

                if (paymentRecord != null) {
                    Order order = orderService.getOrder(paymentRecord.getOrderId());
                    // If order is null, it might not be a regular order, or order processing failed.
                    // For now, we assume if paymentRecord exists, it's a regular order.
                    // More sophisticated logic might be needed if outTradeNo can be ambiguous.
                    if (order == null) {
                        log.warn("Order not found for payment record ID: {}, but payment record exists. Proceeding with payment record update.", paymentRecord.getOrderId());
                        // Even if order is null, update payment record status if it's a success/refund
                         if (payOrderRespDTO.getStatus() == PayOrderStatusRespEnum.SUCCESS){
                            paymentRecord.setPaymentStatus(PaymentStatusEnum.SUCCESS);
                            paymentRecord.setTransactionId(payOrderRespDTO.getChannelOrderNo());
                            paymentRecord.setOutTradeNo(payOrderRespDTO.getOutTradeNo());
                            paymentRecord.setPaymentTime(DateUtil.parse(DateUtil.formatLocalDateTime(payOrderRespDTO.getSuccessTime())));
                            this.updateById(paymentRecord);
                            log.info("Payment record {} updated to SUCCESS, but order not found or not processed.", recordId);
                        } else if (payOrderRespDTO.getStatus() == PayOrderStatusRespEnum.REFUND){
                            paymentRecord.setPaymentStatus(PaymentStatusEnum.REFUND);
                            paymentRecord.setRefundStatus("REFUNDED");
                             paymentRecord.setTransactionId(payOrderRespDTO.getChannelOrderNo());
                             paymentRecord.setOutTradeNo(payOrderRespDTO.getOutTradeNo());
                            this.updateById(paymentRecord);
                            log.info("Payment record {} updated to REFUND, but order not found or not processed.", recordId);
                        }
                        return; // Exit after handling as a payment record update even if order is not found
                    }

                    paymentRecord.setTransactionId(payOrderRespDTO.getChannelOrderNo());
                    paymentRecord.setOutTradeNo(payOrderRespDTO.getOutTradeNo());
                    if (payOrderRespDTO.getStatus() == PayOrderStatusRespEnum.SUCCESS){
                        order.setPaymentStatus(PaymentStatusEnum.SUCCESS);
                        order.setPaymentAmount(paymentRecord.getTotalAmount());
                        paymentRecord.setPaymentStatus(PaymentStatusEnum.SUCCESS);
                        paymentRecord.setPaymentTime(DateUtil.parse(DateUtil.formatLocalDateTime(payOrderRespDTO.getSuccessTime())));
                        this.updateById(paymentRecord);
                        orderService.updateById(order);
                        userService.openVip(order);
                    }else if (payOrderRespDTO.getStatus() == PayOrderStatusRespEnum.REFUND){
                        order.setPaymentStatus(PaymentStatusEnum.REFUND);
                        paymentRecord.setPaymentStatus(PaymentStatusEnum.REFUND);
                        paymentRecord.setRefundStatus("REFUNDED");
                        orderService.updateById(order);
                        this.updateById(paymentRecord);
                    }
                    return; // Successfully processed as regular order
                }
            } catch (NumberFormatException e) {
                // outTradeNo is not a long, proceed to check if it's an article payment
                log.info("outTradeNo '{}' is not a Long, attempting to process as article payment.", outTradeNo);
            } catch (Exception e) {
                // Other exceptions during regular order processing
                log.error("Error processing WeChat callback for outTradeNo '{}' as regular payment: {}", outTradeNo, e.getMessage(), e);
                // Fall through to try article payment, as it might be an issue with fetching PaymentRecord or Order
            }

            // If not processed as a regular order, try processing as an article payment
            // We directly pass the raw body to articlePaymentService as it expects the XML string
            log.info("Attempting to delegate WeChat callback to ArticlePaymentService for outTradeNo: {}", outTradeNo);
            String articlePaymentResult = articlePaymentService.handleWechatPayCallback(body);
            log.info("ArticlePaymentService handleWechatPayCallback result: {}", articlePaymentResult);
            // Depending on the expected result from articlePaymentService.handleWechatPayCallback,
            // you might not need to do anything further here if it handles its own response.
            // If it returns "success" or "fail" or similar, that's logged.

        } catch (WxPayException e) {
            log.error("WeChat Pay callback processing failed due to WxPayException: {}", e.getMessage(), e);
            // Depending on how OpenApiController handles responses, you might need to rethrow or return an error indicator.
            // For now, just logging the error.
            throw new RuntimeException(e); // Rethrow to indicate failure at a higher level if necessary
        } catch (Exception e) {
            // Catch any other unexpected exceptions
            log.error("Unexpected error processing WeChat callback: {}", e.getMessage(), e);
            throw new RuntimeException(e); // Rethrow
        }
    }

    private PaymentRecord queryPendingByOrderId(Long orderId) {
        List<PaymentRecord> list = list(
                Wrappers.<PaymentRecord>lambdaQuery()
                        .eq(PaymentRecord::getOrderId, orderId)
                        .eq(PaymentRecord::getPaymentStatus, PaymentStatusEnum.PENDING)
        );
        return this.batchClose(list);
    }

    private PaymentRecord batchClose(List<PaymentRecord> paymentRecords) {
        if (paymentRecords.isEmpty()) {
            return null;
        }
        if (paymentRecords.size() == 1) {
            return paymentRecords.get(0);
        }
        List<Long> recordList = paymentRecords.stream().sorted(Comparator.comparing(PaymentRecord::getCreateTime))
                .map(PaymentRecord::getRecordId)
                .collect(Collectors.toList());
        Wrapper<PaymentRecord> updateWrapper = Wrappers.<PaymentRecord>lambdaUpdate()
                .set(PaymentRecord::getPaymentStatus, PaymentStatusEnum.CLOSED)
                .in(PaymentRecord::getRecordId, recordList);
        this.update(updateWrapper);
        return paymentRecords.get(paymentRecords.size() - 1);
    }
    @Override
    public Page<PayRecordListByUserIdVo> payRecordListByUserId(PayRecordListByUserIdParams payRecordListByUserIdParams) {

        LambdaQueryWrapper<PaymentRecord> paymentRecordLambdaQueryWrapper = new LambdaQueryWrapper<>();

//        设值条件
        paymentRecordLambdaQueryWrapper.eq(PaymentRecord::getUserId, payRecordListByUserIdParams.getUserId());
        paymentRecordLambdaQueryWrapper.eq(!ObjectUtils.isEmpty(payRecordListByUserIdParams.getOrderId()), PaymentRecord::getOrderId, payRecordListByUserIdParams.getOrderId());
        paymentRecordLambdaQueryWrapper.eq(!StringUtils.isEmpty(payRecordListByUserIdParams.getPaymentStatus()), PaymentRecord::getPaymentStatus, payRecordListByUserIdParams.getPaymentStatus());
        paymentRecordLambdaQueryWrapper.ge(!ObjectUtils.isEmpty(payRecordListByUserIdParams.getPaymentTime()), PaymentRecord::getPaymentTime, payRecordListByUserIdParams.getPaymentTime());
        paymentRecordLambdaQueryWrapper.ge(!ObjectUtils.isEmpty(payRecordListByUserIdParams.getCreateTime()), PaymentRecord::getCreateTime, payRecordListByUserIdParams.getCreateTime());
        paymentRecordLambdaQueryWrapper.eq(!StringUtils.isEmpty(payRecordListByUserIdParams.getPayChannel()), PaymentRecord::getPayChannel, payRecordListByUserIdParams.getPayChannel());

        paymentRecordLambdaQueryWrapper.orderByDesc(PaymentRecord::getCreateTime);
//        获得分页参数
        PageVO pageVO = PageUtils.getPageVO();
        Page<PaymentRecord> paymentRecordPage = new Page<>(pageVO.getPageNum(), pageVO.getPageSize());
        paymentRecordPage = this.baseMapper.selectPage(paymentRecordPage, paymentRecordLambdaQueryWrapper);

//        进行转化
        Page<PayRecordListByUserIdVo> result = new Page<>();
        result.setTotal(paymentRecordPage.getTotal());
        result.setRecords(paymentRecordPage.getRecords().stream().map(paymentRecord -> {
            PayRecordListByUserIdVo payRecordListByUserIdVo = new PayRecordListByUserIdVo();
            BeanUtils.copyProperties(paymentRecord, payRecordListByUserIdVo);
//            payRecordListByUserIdVo.setCreateTime(paymentRecord.getCreateTime());
            payRecordListByUserIdVo.setPaymentStatus(paymentRecord.getPaymentStatus().getCode());
            return payRecordListByUserIdVo;
        }).collect(Collectors.toList()));
        return result;
    }
}
