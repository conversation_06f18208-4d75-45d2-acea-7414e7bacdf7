package com.longsheng.tools.modules.appEntry.service.impl;

import com.longsheng.tools.common.exception.CustomException;
import com.longsheng.tools.modules.appEntry.entity.PromoteBusinessParam;
import com.longsheng.tools.modules.appEntry.enums.WeiOfficialTypeEnum;
import com.longsheng.tools.modules.appEntry.service.GetCopyAndEntryService;
import com.longsheng.tools.modules.appEntry.service.PromoteBusinessParamService;
import com.longsheng.tools.modules.appEntry.strategy.GetContentStrategy;
import com.longsheng.tools.modules.appEntry.vo.GetContentVo;
import com.longsheng.tools.modules.appEntry.vo.WeiOfficialVo;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

@Service
public class GetCopyAndEntryServiceImpl implements GetCopyAndEntryService {
    @Resource
    private PromoteBusinessParamService promoteBusinessParamService;
    @Resource
    private WeiOfficialServiceImpl weiOfficialService;

    @Override
    public GetContentVo getCopy(Long promotionBusinessParamId, long userId) {
        PromoteBusinessParam byId = promoteBusinessParamService.getById(promotionBusinessParamId);
        if (byId == null) {
            throw new CustomException("该业务参数不存在");
        }
        Long weiOfficialId = byId.getWeiOfficialId();
        WeiOfficialVo weiOfficialById = weiOfficialService.getWeiOfficialById(weiOfficialId);
//       不同的type有不同的策略
        GetContentStrategy enumByCode = WeiOfficialTypeEnum.getEnumByCode(weiOfficialById.getType());
        if (enumByCode == null) {
            throw new CustomException("公众号类型有误");
        }
        return enumByCode.getContent(byId.getPromotionPlatform(), byId.getPromoteBusinessId(), userId);
    }
}
