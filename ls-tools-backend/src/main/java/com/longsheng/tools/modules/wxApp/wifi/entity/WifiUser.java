package com.longsheng.tools.modules.wxApp.wifi.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.longsheng.tools.common.base.TenantBaseDO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.util.Date;

@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
@AllArgsConstructor
@TableName("wifi_user")
public class WifiUser extends TenantBaseDO<WifiUser> {
    @TableId(type = IdType.ASSIGN_ID)
    private Long id;
    private String openId;
    private String nickName;
    private String avatarUrl;
    @TableLogic
    private Integer deleted;

    private Date createTime;

    private Date updateTime;

    public void init(String nickName, String openId, Long tenantId) {
        this.setNickName(nickName);
        this.setOpenId(openId);
        this.setAvatarUrl(null);
        this.setCreateTime(new Date());
        this.setUpdateTime(new Date());
        this.setTenantId(tenantId);
    }
} 