package com.longsheng.tools.modules.wxApp.user.service;


import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.longsheng.tools.common.elementui.LabelVo;
import com.longsheng.tools.modules.wxApp.user.dto.BindPhoneDto;
import com.longsheng.tools.modules.wxApp.user.dto.ListWXAppUserDTO;
import com.longsheng.tools.modules.wxApp.user.dto.UpdateUserInfoByIDTO;
import com.longsheng.tools.modules.wxApp.user.dto.WxLoginDTO;
import com.longsheng.tools.modules.wxApp.user.entity.WXAppUser;
import com.longsheng.tools.modules.wxApp.user.vo.ListWXAppUserVo;
import org.springframework.stereotype.Service;

import java.util.List;


@Service
public interface WXAppUserService {
    WXAppUser getById(Long userId);

    WXAppUser login(WxLoginDTO dto);

    void updateUserInfo(Long userId, UpdateUserInfoByIDTO updateUserInfoByIDTO);

    Page<ListWXAppUserVo> listWXAppUser(ListWXAppUserDTO listWXAppUserDTO);

    void deleteWXAppUser(Long id);

    List<LabelVo<Long>> selectLabels();

    void sendCode(String phone);

    void bindPhone(BindPhoneDto bindPhoneDto);
}