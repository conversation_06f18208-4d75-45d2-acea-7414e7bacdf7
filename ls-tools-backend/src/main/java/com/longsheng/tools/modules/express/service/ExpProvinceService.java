package com.longsheng.tools.modules.express.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.longsheng.tools.modules.express.entity.ExpProvinceEntity;
import com.longsheng.tools.modules.express.vo.AddressVO;

import java.util.List;

public interface ExpProvinceService extends IService<ExpProvinceEntity> {

    /**
     * 根据地区名称和等级匹配地区编码
     *
     * @param originName 地区名称
     * @param level      等级，0-省、1-市、2-区/县
     * @return
     */
    String queryCodeByName(String originName, Integer level);

    String getByCode(String s);

    List<String> getCityByCode(String s);

    String getProvinceCodeByCityCode(String s);

    ExpProvinceEntity getExpProvinceEntity(String code);

    String getByName(String putCity);

    String dealCodeByName(String sendCity, Integer level);

    ExpProvinceEntity dealCodeByNameP(String sendCity, Integer level, Integer pId);

    ExpProvinceEntity getByNameOrId(String putProvince, Integer level);

    /**
     * 根据名称和省ID查询县编码
     *
     * @param regionName 县名称
     * @param provinceId 省ID
     * @return
     */
    ExpProvinceEntity getRegionByNameAndProvince(String regionName, Integer provinceId);

    /**
     * 查询全部地址数据*
     *
     * @return
     */
    List<ExpProvinceEntity> getProvinceAll();

    AddressVO queryByName(String name);
}

