package com.longsheng.tools.modules.customer.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

@Accessors(chain = true)
@Data
public class UserInfoVo implements Serializable {

    private Long id;

    private String username;

    private String avatar;

    private String mobile;

    private Long parentId;

    private Long level;

    private BigDecimal shareCost;

    private Boolean disable;

    private Long teamId;

    private String teamName;

    private Boolean isVip;

    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date vipBegin;

    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date vipEnd;

    private Integer freeChances;

    private Date createTime;

}
