package com.longsheng.tools.modules.wxApp.user.vo;


import com.longsheng.tools.common.interfaces.BeanAdapter;
import com.longsheng.tools.modules.wxApp.user.entity.WXAppUser;
import lombok.Data;
import org.springframework.util.ObjectUtils;

@Data
public class UserInfoVo implements BeanAdapter<WXAppUser, UserInfoVo> {
    private String nickName;
    private String avatarUrl;
    private boolean auth;

    @Override
    public UserInfoVo adapt(WXAppUser parkUser) {
        this.nickName = parkUser.getNickName();
        this.avatarUrl = parkUser.getAvatarUrl();
        this.auth = !ObjectUtils.isEmpty(parkUser.getPhone());
        return this;
    }

}
