//package com.longsheng.tools.modules.appEntry.vo;
//
//import com.longsheng.tools.common.interfaces.BeanAdapter;
//import lombok.Data;
//
//import java.io.Serializable;
//
//@Data
//public class WxMpBaseVo implements BeanAdapter<WeiOfficialVo>, Serializable {
//    private Long id;
//
//    private String title;
//
//    private String name;
//
//    @Override
//    public void adapt(WeiOfficialVo vo) {
//        this.id = vo.getId();
//        this.title = vo.getApplicationName();
//        this.name = vo.getName();
//    }
//}
