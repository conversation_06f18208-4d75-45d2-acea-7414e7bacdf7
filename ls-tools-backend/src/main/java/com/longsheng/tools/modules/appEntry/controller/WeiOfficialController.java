package com.longsheng.tools.modules.appEntry.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.longsheng.tools.common.utils.RES;
import com.longsheng.tools.modules.appEntry.params.AddWeiOfficialParam;
import com.longsheng.tools.modules.appEntry.params.DeleteWeiOfficialParams;
import com.longsheng.tools.modules.appEntry.params.GetWeiOfficialListParam;
import com.longsheng.tools.modules.appEntry.service.WeiOfficialService;
import com.longsheng.tools.modules.appEntry.vo.WeiOfficialTypeVo;
import com.longsheng.tools.modules.appEntry.vo.WeiOfficialVo;
import com.longsheng.tools.modules.appEntry.vo.WeiOfficialIdAndNameVo;
import com.longsheng.tools.system.vo.TableInfo;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Update;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

@RestController
@RequestMapping("/weiOfficial")
public class WeiOfficialController {


    @Resource
    private WeiOfficialService weiOfficialService;


    @GetMapping("/getAllWeiOfficialIdAndNameById")
    public RES getAllWeiOfficialIdAndNameById(@RequestParam(value = "tenantId", required = true, defaultValue = "-1") Long tenantId) {
        List<WeiOfficialIdAndNameVo> weiUserIdAndNameVoList = weiOfficialService.getAllWeiOfficialIdAndNameById(tenantId);
        return RES.ok(weiUserIdAndNameVoList);
    }

    @GetMapping("/getAllWeiOfficialIdAndName")
    public RES getAllWeiOfficialIdAndName() {
        List<WeiOfficialIdAndNameVo> weiUserIdAndNameVoList = weiOfficialService.getAllWeiOfficialIdAndName();
        return RES.ok(weiUserIdAndNameVoList);
    }

    // weiOfficial
    @GetMapping("/getWeiOfficialList")
    public TableInfo getWeiOfficialList(@Validated GetWeiOfficialListParam getWeiOfficialListParam) {
        Page<WeiOfficialVo> weiOfficialVoPage = weiOfficialService.getWeiOfficialList(getWeiOfficialListParam);
        return TableInfo.ok(weiOfficialVoPage);
    }

    //    单个查询
    @GetMapping("/getWeiOfficialById/{id}")
    public RES getWeiOfficialById(@PathVariable("id") Long id) {
        WeiOfficialVo weiOfficialVo = weiOfficialService.getWeiOfficialById(id);
        return RES.ok(weiOfficialVo);
    }

    //    增加公众号
    @PostMapping("/addWeiOfficial")
    public RES addWeiOfficial(@RequestBody @Validated(Insert.class) AddWeiOfficialParam addWeiOfficialParam) {
        weiOfficialService.addWeiOfficial(addWeiOfficialParam);
        return RES.ok();
    }

    //    删除公众号可以批量删除）
    @PostMapping("/deleteWeiOfficial")
    public RES deleteWeiOfficial(@RequestBody DeleteWeiOfficialParams deleteWeiOfficialParam) {
        weiOfficialService.deleteWeiOfficial(deleteWeiOfficialParam);
        return RES.ok();
    }

    //    更新公众号
    @PostMapping("/updateWeiOfficialById")
    public RES updateWeiOfficialById(@RequestBody @Validated(Update.class) AddWeiOfficialParam addWeiOfficialParam) {
        weiOfficialService.updateWeiOfficialById(addWeiOfficialParam);
        return RES.ok();
    }

    //    获得所有公众号所有获取文案的方式
    @GetMapping("/getAllWeiOfficialType")
    public RES getAllWeiOfficialType() {
        List<WeiOfficialTypeVo> weiOfficialTypeVoList = weiOfficialService.getAllWeiOfficialType();
        return RES.ok(weiOfficialTypeVoList);
    }


}
