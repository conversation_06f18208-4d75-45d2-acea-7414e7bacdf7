package com.longsheng.tools.modules.appEntry.params;

import com.longsheng.tools.common.annotation.EnumValid;
import com.longsheng.tools.modules.appEntry.enums.PromotionPlatformEnum;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

@Data
public class AuthTaskPassByIdParam {
    @EnumValid(message = "宣传平台不合法", valueClass = Integer.class, enumClass = PromotionPlatformEnum.class, enumMethod = "isValid", canIsNull = false)
    private Integer promotionPlatform;
    @NotNull(message = "id不能为空")
    private Long id;
    //    @NotBlank(message = "content不能为空")
//    private String content;
    @NotNull
    private Boolean authContent;
    @NotNull(message = "imageAuth不x能为空")
    private Boolean imageAuth;
    @NotNull(message = "autoAuth不能为空")
    private Boolean autoAuth;
    //    private String title;
    @NotNull(message = "authTitle")
    private Boolean authTitle;
    @NotNull(message = "quanZiAuth不能为空")
    private Boolean quanZiAuth;
    @NotNull(message = "weiUserId不能为空")
    private Long weiUserId;
}
