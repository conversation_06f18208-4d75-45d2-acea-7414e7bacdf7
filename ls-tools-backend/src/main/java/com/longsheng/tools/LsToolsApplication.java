package com.longsheng.tools;

import com.longsheng.tools.common.annotation.EnableDistributedLock;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.ConfigurableApplicationContext;
import org.springframework.web.socket.config.annotation.EnableWebSocket;

/**
 * 项目启动类
 *
 * <AUTHOR>
 */
@EnableDistributedLock
@EnableWebSocket
@Slf4j
@SpringBootApplication
public class LsToolsApplication {

    public static void main(String[] args) {
        ConfigurableApplicationContext run = SpringApplication.run(LsToolsApplication.class, args);
    }
}
