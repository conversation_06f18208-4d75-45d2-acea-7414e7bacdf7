<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.longsheng.tools.modules.customer.mapper.FrontUserTeamMapper">
    <select id="listUserAddTeam" resultType="com.longsheng.tools.modules.customer.vo.UserAddTeamVo">
        select
            t.id as teamId,
            t.team_name as teamName,
            t.logo as teamLogo,
            t.is_vip as teamIsVip,
            t.create_time as teamCreateTime,
            t.owner_name as teamOwnerName,
            t.user_count as teamUserCount,
            t.owner as teamOwnerId
        from
        c_user_team ut left join  c_team t on ut.team_id = t.id
        where
        t.del_flag = 0
        <if test="userAddTeamParam.teamName != null">
            and t.team_name like concat('%',#{userAddTeamParam.teamName},'%')
        </if>
        <if test="userAddTeamParam.userCount != null and userAddTeamParam.isLe != null and userAddTeamParam.isLe == false">
            and t.user_count >= #{userAddTeamParam.userCount}
        </if>
        <if test="userAddTeamParam.userCount != null and userAddTeamParam.isLe != null and userAddTeamParam.isLe == true">
            and t.user_count >= #{userAddTeamParam.userCount}
        </if>
        <if test="userAddTeamParam.ownerName != null">
            and t.owner_name like concat('%',#{userAddTeamParam.ownerName},'%')
        </if>
        <if test="userAddTeamParam.status != null">
            and ut.status = #{userAddTeamParam.status}
        </if>
        <if test="userAddTeamParam.userId != null">
            and ut.user_id = #{userAddTeamParam.userId}
        </if>
    </select>


    <select id="inviteListUser" resultType="com.longsheng.tools.modules.customer.vo.InviteListUserVo">
        select
        u.id as  userId,
        u.avatar as avatar,
        u.username as username,
        u.level as level,
        ut.status as status
        from
        c_user_team ut left join  c_user u on ut.user_id = u.id
        where
        u.del_flag = 0 and ut.is_owner = 0
        <if test="inviteListUserParam.teamId != null">
            and ut.team_id = #{inviteListUserParam.teamId}
        </if>
        <if test="inviteListUserParam.status != null">
            and ut.status = #{inviteListUserParam.status}
        </if>

        <if test="inviteListUserParam.userName != null">
            and u.username like concat('%',#{inviteListUserParam.username},'%')
        </if>
    </select>
</mapper>