CREATE TABLE `quota_change_record` (
  `id` bigint NOT NULL,
  `count` int NOT NULL DEFAULT '0',
  `allocation_method` int DEFAULT NULL COMMENT '0手动，1充值，2商户扣减',
  `user_id` int DEFAULT NULL,
  `user_name` varchar(255) DEFAULT NULL,
  `create_time` datetime DEFAULT NULL,
  `create_user` varchar(255) DEFAULT NULL,
  `to_user` int DEFAULT NULL,
  `tenant_id` bigint(20) NOT NULL COMMENT '租户ID',
  `del_flag` tinyint(1) NOT NULL DEFAULT '0',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

CREATE TABLE `wechat_crash_log` (
                                    `id` int DEFAULT NULL,
                                    `error_msg` text,
                                    `ip` varchar(255) DEFAULT NULL,
                                    `create_date` datetime DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;